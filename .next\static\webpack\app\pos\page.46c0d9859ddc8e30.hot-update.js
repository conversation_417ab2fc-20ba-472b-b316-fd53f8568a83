"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Try to load from Supabase first\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n            if (error) throw error;\n            setProducts(data || []);\n        } catch (error) {\n            // Fallback to localStorage for development\n            try {\n                const storedProducts = localStorage.getItem(\"gym_products\");\n                if (storedProducts) {\n                    const productData = JSON.parse(storedProducts);\n                    setProducts(productData.filter((p)=>p.stock > 0));\n                } else {\n                    // Create sample products for development\n                    const sampleProducts = [\n                        {\n                            id: \"1\",\n                            name: \"Protein Powder\",\n                            category: \"Supplements\",\n                            price_dzd: 5000,\n                            stock: 25,\n                            barcode: \"1234567890123\",\n                            expiry_date: \"2024-12-31\"\n                        },\n                        {\n                            id: \"2\",\n                            name: \"Energy Drink\",\n                            category: \"Beverages\",\n                            price_dzd: 200,\n                            stock: 50,\n                            barcode: \"2345678901234\",\n                            expiry_date: \"2024-06-30\"\n                        },\n                        {\n                            id: \"3\",\n                            name: \"Gym Towel\",\n                            category: \"Accessories\",\n                            price_dzd: 800,\n                            stock: 15,\n                            barcode: \"3456789012345\"\n                        }\n                    ];\n                    setProducts(sampleProducts);\n                    localStorage.setItem(\"gym_products\", JSON.stringify(sampleProducts));\n                }\n            } catch (localError) {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to load products\",\n                    variant: \"destructive\"\n                });\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Update product stock in localStorage\n            const updatedProducts = products.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity\n                    };\n                }\n                return product;\n            });\n            localStorage.setItem(\"gym_products\", JSON.stringify(updatedProducts));\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 382,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 381,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 overflow-auto max-h-[calc(100vh-16rem)]\",\n                                children: filteredProducts.map((product)=>{\n                                    const expired = isProductExpired(product);\n                                    const expiringSoon = isProductExpiringSoon(product);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\"),\n                                        onClick: ()=>!expired && addToCart(product),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-gray-900 dark:text-white line-clamp-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    t(\"expiry_date\"),\n                                                                    \": \",\n                                                                    new Date(product.expiry_date).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-red-600 dark:text-red-400\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            t(\"stock\"),\n                                                                            \": \",\n                                                                            product.stock\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 555,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 580,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 664,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 663,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 728,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 736,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 390,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ })

});