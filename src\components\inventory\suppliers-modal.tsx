'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import { InventoryStorage, Supplier, Purchase, SupplierPayment } from '@/lib/inventory-storage'
import { formatCurrency } from '@/lib/utils'
import {
  X,
  Plus,
  Edit,
  Trash2,
  Eye,
  Users,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  DollarSign,
  Calendar,
  FileText,
  Building,
  Hash,
  Save,
  Search,
} from 'lucide-react'

interface SuppliersModalProps {
  onClose: () => void
  onUpdate: () => void
}

export function SuppliersModal({ onClose, onUpdate }: SuppliersModalProps) {
  const { t } = useLanguage()
  const { toast } = useToast()

  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null)
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null)
  const [showTransactions, setShowTransactions] = useState(false)
  const [showPaymentForm, setShowPaymentForm] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    contact_person: '',
    phone: '',
    email: '',
    address: '',
    tax_number: '',
    payment_terms: 'cash' as const,
    credit_limit: '0',
    notes: '',
  })

  // Payment form state
  const [paymentData, setPaymentData] = useState({
    amount: '',
    payment_method: 'cash' as const,
    reference_number: '',
    notes: '',
  })

  useEffect(() => {
    loadSuppliers()
  }, [])

  const loadSuppliers = () => {
    const suppliersData = InventoryStorage.getSuppliers()
    setSuppliers(suppliersData.filter(s => s.active))
  }

  const resetForm = () => {
    setFormData({
      name: '',
      contact_person: '',
      phone: '',
      email: '',
      address: '',
      tax_number: '',
      payment_terms: 'cash',
      credit_limit: '0',
      notes: '',
    })
    setEditingSupplier(null)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleEdit = (supplier: Supplier) => {
    setFormData({
      name: supplier.name,
      contact_person: supplier.contact_person || '',
      phone: supplier.phone || '',
      email: supplier.email || '',
      address: supplier.address || '',
      tax_number: supplier.tax_number || '',
      payment_terms: supplier.payment_terms,
      credit_limit: supplier.credit_limit.toString(),
      notes: supplier.notes || '',
    })
    setEditingSupplier(supplier)
    setShowAddForm(true)
  }

  const handleSave = () => {
    if (!formData.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Supplier name is required',
        variant: 'destructive',
      })
      return
    }

    try {
      const supplierData = {
        name: formData.name.trim(),
        contact_person: formData.contact_person || undefined,
        phone: formData.phone || undefined,
        email: formData.email || undefined,
        address: formData.address || undefined,
        tax_number: formData.tax_number || undefined,
        payment_terms: formData.payment_terms,
        credit_limit: parseFloat(formData.credit_limit) || 0,
        current_balance: editingSupplier?.current_balance || 0,
        notes: formData.notes || undefined,
        active: true,
      }

      if (editingSupplier) {
        InventoryStorage.updateSupplier(editingSupplier.id, supplierData)
        toast({
          title: 'Success',
          description: 'Supplier updated successfully',
        })
      } else {
        InventoryStorage.addSupplier(supplierData)
        toast({
          title: 'Success',
          description: 'Supplier added successfully',
        })
      }

      loadSuppliers()
      setShowAddForm(false)
      resetForm()
      onUpdate()
    } catch (error) {
      console.error('Error saving supplier:', error)
      toast({
        title: 'Error',
        description: 'Failed to save supplier',
        variant: 'destructive',
      })
    }
  }

  const handleDelete = (supplier: Supplier) => {
    if (confirm(`Are you sure you want to delete supplier "${supplier.name}"?`)) {
      InventoryStorage.deleteSupplier(supplier.id)
      loadSuppliers()
      toast({
        title: 'Success',
        description: 'Supplier deleted successfully',
      })
      onUpdate()
    }
  }

  const handlePayment = () => {
    if (!selectedSupplier || !paymentData.amount) {
      toast({
        title: 'Validation Error',
        description: 'Amount is required',
        variant: 'destructive',
      })
      return
    }

    try {
      InventoryStorage.addSupplierPayment({
        supplier_id: selectedSupplier.id,
        payment_date: new Date().toISOString().split('T')[0],
        amount: parseFloat(paymentData.amount),
        payment_method: paymentData.payment_method,
        reference_number: paymentData.reference_number || undefined,
        notes: paymentData.notes || undefined,
      })

      toast({
        title: 'Success',
        description: 'Payment recorded successfully',
      })

      loadSuppliers()
      setShowPaymentForm(false)
      setPaymentData({
        amount: '',
        payment_method: 'cash',
        reference_number: '',
        notes: '',
      })
      onUpdate()
    } catch (error) {
      console.error('Error recording payment:', error)
      toast({
        title: 'Error',
        description: 'Failed to record payment',
        variant: 'destructive',
      })
    }
  }

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    supplier.contact_person?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    supplier.phone?.includes(searchQuery)
  )

  const getSupplierTransactions = (supplierId: string) => {
    const purchases = InventoryStorage.getPurchases().filter(p => p.supplier_id === supplierId)
    const payments = InventoryStorage.getSupplierPaymentsBySupplier(supplierId)
    return { purchases, payments }
  }

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          // Don't close modal when clicking outside
        }
      }}
    >
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-y-auto glass border-white/20">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Users className="w-6 h-6" />
            <span>Suppliers Management</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {!showAddForm && !showTransactions && !showPaymentForm && (
            <>
              {/* Header Actions */}
              <div className="flex justify-between items-center">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search suppliers..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <Button
                  onClick={() => setShowAddForm(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Supplier
                </Button>
              </div>

              {/* Suppliers Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4">Supplier</th>
                      <th className="text-left py-3 px-4">Contact</th>
                      <th className="text-left py-3 px-4">Payment Terms</th>
                      <th className="text-left py-3 px-4">Balance</th>
                      <th className="text-left py-3 px-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredSuppliers.map((supplier) => (
                      <tr key={supplier.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium">{supplier.name}</p>
                            {supplier.contact_person && (
                              <p className="text-sm text-gray-500">{supplier.contact_person}</p>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="space-y-1">
                            {supplier.phone && (
                              <div className="flex items-center text-sm">
                                <Phone className="w-3 h-3 mr-1" />
                                {supplier.phone}
                              </div>
                            )}
                            {supplier.email && (
                              <div className="flex items-center text-sm">
                                <Mail className="w-3 h-3 mr-1" />
                                {supplier.email}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-sm ${
                            supplier.payment_terms === 'cash' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {supplier.payment_terms.replace('_', ' ').toUpperCase()}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div>
                            <span className={`font-medium ${
                              supplier.current_balance > 0 ? 'text-red-500' : 'text-green-500'
                            }`}>
                              {formatCurrency(supplier.current_balance)}
                            </span>
                            {supplier.payment_terms !== 'cash' && (
                              <p className="text-xs text-gray-500">
                                Limit: {formatCurrency(supplier.credit_limit)}
                              </p>
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedSupplier(supplier)
                                setShowTransactions(true)
                              }}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(supplier)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            {supplier.current_balance > 0 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedSupplier(supplier)
                                  setShowPaymentForm(true)
                                }}
                                className="text-green-600 hover:text-green-700"
                              >
                                <DollarSign className="w-4 h-4" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(supplier)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {filteredSuppliers.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No suppliers found
                  </div>
                )}
              </div>
            </>
          )}

          {/* Add/Edit Supplier Form */}
          {showAddForm && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  {editingSupplier ? 'Edit Supplier' : 'Add New Supplier'}
                </h3>
                <Button
                  variant="ghost"
                  onClick={() => {
                    setShowAddForm(false)
                    resetForm()
                  }}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <Building className="w-4 h-4 inline mr-2" />
                      Supplier Name *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter supplier name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Contact Person
                    </label>
                    <input
                      type="text"
                      value={formData.contact_person}
                      onChange={(e) => handleInputChange('contact_person', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter contact person name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <Phone className="w-4 h-4 inline mr-2" />
                      Phone
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter phone number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <Mail className="w-4 h-4 inline mr-2" />
                      Email
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter email address"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <MapPin className="w-4 h-4 inline mr-2" />
                      Address
                    </label>
                    <textarea
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      placeholder="Enter address"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <Hash className="w-4 h-4 inline mr-2" />
                      Tax Number
                    </label>
                    <input
                      type="text"
                      value={formData.tax_number}
                      onChange={(e) => handleInputChange('tax_number', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter tax number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <CreditCard className="w-4 h-4 inline mr-2" />
                      Payment Terms
                    </label>
                    <select
                      value={formData.payment_terms}
                      onChange={(e) => handleInputChange('payment_terms', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="cash">Cash</option>
                      <option value="credit_30">Credit 30 days</option>
                      <option value="credit_60">Credit 60 days</option>
                      <option value="credit_90">Credit 90 days</option>
                    </select>
                  </div>

                  {formData.payment_terms !== 'cash' && (
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Credit Limit (DZD)
                      </label>
                      <input
                        type="number"
                        value={formData.credit_limit}
                        onChange={(e) => handleInputChange('credit_limit', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="0"
                        min="0"
                      />
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <FileText className="w-4 h-4 inline mr-2" />
                      Notes
                    </label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      placeholder="Enter notes"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-6 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowAddForm(false)
                    resetForm()
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {editingSupplier ? 'Update Supplier' : 'Add Supplier'}
                </Button>
              </div>
            </div>
          )}

          {/* Payment Form */}
          {showPaymentForm && selectedSupplier && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  Record Payment - {selectedSupplier.name}
                </h3>
                <Button
                  variant="ghost"
                  onClick={() => setShowPaymentForm(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Current Balance</p>
                <p className="text-2xl font-bold text-red-500">
                  {formatCurrency(selectedSupplier.current_balance)}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Payment Amount (DZD) *
                  </label>
                  <input
                    type="number"
                    value={paymentData.amount}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, amount: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                    min="0"
                    max={selectedSupplier.current_balance}
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Payment Method
                  </label>
                  <select
                    value={paymentData.payment_method}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, payment_method: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="cash">Cash</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="check">Check</option>
                    <option value="card">Card</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Reference Number
                  </label>
                  <input
                    type="text"
                    value={paymentData.reference_number}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, reference_number: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter reference number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Notes
                  </label>
                  <input
                    type="text"
                    value={paymentData.notes}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, notes: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter notes"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-6 border-t">
                <Button
                  variant="outline"
                  onClick={() => setShowPaymentForm(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handlePayment}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  <DollarSign className="w-4 h-4 mr-2" />
                  Record Payment
                </Button>
              </div>
            </div>
          )}

          {/* Supplier Transactions */}
          {showTransactions && selectedSupplier && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  Transactions - {selectedSupplier.name}
                </h3>
                <Button
                  variant="ghost"
                  onClick={() => setShowTransactions(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="glass border-white/20">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Current Balance</p>
                      <p className="text-2xl font-bold text-red-500">
                        {formatCurrency(selectedSupplier.current_balance)}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass border-white/20">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Credit Limit</p>
                      <p className="text-2xl font-bold">
                        {formatCurrency(selectedSupplier.credit_limit)}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass border-white/20">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Available Credit</p>
                      <p className="text-2xl font-bold text-green-500">
                        {formatCurrency(selectedSupplier.credit_limit - selectedSupplier.current_balance)}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Transaction History */}
              <div className="space-y-4">
                <h4 className="font-medium">Recent Transactions</h4>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-2 px-4">Date</th>
                        <th className="text-left py-2 px-4">Type</th>
                        <th className="text-left py-2 px-4">Amount</th>
                        <th className="text-left py-2 px-4">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(() => {
                        const { purchases, payments } = getSupplierTransactions(selectedSupplier.id)
                        const allTransactions = [
                          ...purchases.map(p => ({ ...p, type: 'purchase' as const })),
                          ...payments.map(p => ({ ...p, type: 'payment' as const }))
                        ].sort((a, b) => new Date(b.created_at || b.payment_date).getTime() - new Date(a.created_at || a.payment_date).getTime())

                        return allTransactions.slice(0, 10).map((transaction, index) => (
                          <tr key={index} className="border-b border-gray-100">
                            <td className="py-2 px-4">
                              {new Date(transaction.type === 'purchase' ? transaction.created_at : transaction.payment_date).toLocaleDateString()}
                            </td>
                            <td className="py-2 px-4">
                              <span className={`px-2 py-1 rounded-full text-sm ${
                                transaction.type === 'purchase' 
                                  ? 'bg-blue-100 text-blue-800' 
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {transaction.type === 'purchase' ? 'Purchase' : 'Payment'}
                              </span>
                            </td>
                            <td className="py-2 px-4">
                              <span className={transaction.type === 'purchase' ? 'text-red-500' : 'text-green-500'}>
                                {transaction.type === 'purchase' ? '+' : '-'}
                                {formatCurrency(transaction.type === 'purchase' ? transaction.total_amount : transaction.amount)}
                              </span>
                            </td>
                            <td className="py-2 px-4">
                              {transaction.type === 'purchase' ? (
                                <span className={`px-2 py-1 rounded-full text-sm ${
                                  transaction.payment_status === 'paid' 
                                    ? 'bg-green-100 text-green-800'
                                    : transaction.payment_status === 'partial'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {transaction.payment_status}
                                </span>
                              ) : (
                                <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                                  Completed
                                </span>
                              )}
                            </td>
                          </tr>
                        ))
                      })()}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
