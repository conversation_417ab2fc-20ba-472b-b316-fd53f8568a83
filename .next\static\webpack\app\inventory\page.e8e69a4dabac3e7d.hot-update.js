"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InventoryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_inventory_add_product_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/inventory/add-product-modal */ \"(app-pages-browser)/./src/components/inventory/add-product-modal.tsx\");\n/* harmony import */ var _components_inventory_suppliers_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/inventory/suppliers-modal */ \"(app-pages-browser)/./src/components/inventory/suppliers-modal.tsx\");\n/* harmony import */ var _components_inventory_purchase_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/inventory/purchase-modal */ \"(app-pages-browser)/./src/components/inventory/purchase-modal.tsx\");\n/* harmony import */ var _components_inventory_stock_movement_history__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/inventory/stock-movement-history */ \"(app-pages-browser)/./src/components/inventory/stock-movement-history.tsx\");\n/* harmony import */ var _components_inventory_stock_adjustment_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/inventory/stock-adjustment-modal */ \"(app-pages-browser)/./src/components/inventory/stock-adjustment-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Import components (we'll create these)\n\n\n\n\n\nfunction InventoryPage() {\n    _s();\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // State management\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stockMovements, setStockMovements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showLowStock, setShowLowStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExpiring, setShowExpiring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modal states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuppliers, setShowSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPurchase, setShowPurchase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStockHistory, setShowStockHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStockAdjustment, setShowStockAdjustment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReports, setShowReports] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n        _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.initializeDefaultData();\n    }, []);\n    const loadData = ()=>{\n        try {\n            const productsData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getFromStorage(\"gym_products\");\n            const suppliersData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getSuppliers();\n            const movementsData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getStockMovements();\n            setProducts(productsData);\n            setSuppliers(suppliersData.filter((s)=>s.active));\n            setStockMovements(movementsData);\n        } catch (error) {\n            console.error(\"Error loading inventory data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load inventory data\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Filter products based on search and filters\n    const filteredProducts = products.filter((product)=>{\n        var _product_brand;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || product.category === selectedCategory;\n        const matchesLowStock = !showLowStock || product.stock <= product.min_stock;\n        const matchesExpiring = !showExpiring || product.expiry_date && new Date(product.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n        return matchesSearch && matchesCategory && (!showLowStock || matchesLowStock) && (!showExpiring || matchesExpiring);\n    });\n    // Calculate statistics\n    const stats = {\n        totalProducts: products.length,\n        totalValue: products.reduce((sum, p)=>sum + p.stock * p.price_dzd, 0),\n        lowStockCount: products.filter((p)=>p.stock <= p.min_stock).length,\n        expiringCount: products.filter((p)=>p.expiry_date && new Date(p.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)).length,\n        activeSuppliers: suppliers.length\n    };\n    const categories = [\n        ...new Set(products.map((p)=>p.category))\n    ];\n    const handleProductUpdate = ()=>{\n        loadData();\n        setSelectedProduct(null);\n    };\n    const handleDeleteProduct = (productId)=>{\n        if (confirm(\"Are you sure you want to delete this product?\")) {\n            const updatedProducts = products.filter((p)=>p.id !== productId);\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            loadData();\n            toast({\n                title: \"Success\",\n                description: \"Product deleted successfully\"\n            });\n        }\n    };\n    const getStockStatus = (product)=>{\n        if (product.stock === 0) return {\n            status: \"out\",\n            color: \"text-red-500\",\n            bg: \"bg-red-50\"\n        };\n        if (product.stock <= product.min_stock) return {\n            status: \"low\",\n            color: \"text-orange-500\",\n            bg: \"bg-orange-50\"\n        };\n        return {\n            status: \"good\",\n            color: \"text-green-500\",\n            bg: \"bg-green-50\"\n        };\n    };\n    const getExpiryStatus = (expiryDate)=>{\n        if (!expiryDate) return null;\n        const today = new Date();\n        const expiry = new Date(expiryDate);\n        const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (daysUntilExpiry < 0) return {\n            status: \"expired\",\n            color: \"text-red-500\",\n            text: \"Expired\"\n        };\n        if (daysUntilExpiry <= 7) return {\n            status: \"expiring\",\n            color: \"text-orange-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n        if (daysUntilExpiry <= 30) return {\n            status: \"warning\",\n            color: \"text-yellow-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n        return {\n            status: \"good\",\n            color: \"text-green-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: \"Inventory Management\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-8 h-8 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.totalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(stats.totalValue)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-8 h-8 text-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Low Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.lowStockCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-8 h-8 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Expiring Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.expiringCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-8 h-8 text-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Suppliers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.activeSuppliers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowAddProduct(true),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add New Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowPurchase(true),\n                                className: \"bg-green-500 hover:bg-green-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"New Purchase\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowSuppliers(true),\n                                className: \"bg-purple-500 hover:bg-purple-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Manage Suppliers\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowStockHistory(true),\n                                variant: \"outline\",\n                                className: \"border-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Stock History\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowReports(true),\n                                variant: \"outline\",\n                                className: \"border-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reports\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"glass border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1 min-w-64\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search products...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: showLowStock ? \"default\" : \"outline\",\n                                        onClick: ()=>setShowLowStock(!showLowStock),\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Low Stock\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: showExpiring ? \"default\" : \"outline\",\n                                        onClick: ()=>setShowExpiring(!showExpiring),\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Expiring\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"glass border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Products (\",\n                                                filteredProducts.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Export\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Product\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Stock\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Price\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Expiry\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: filteredProducts.map((product)=>{\n                                                        const stockStatus = getStockStatus(product);\n                                                        const expiryStatus = getExpiryStatus(product.expiry_date);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-100 hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            product.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: product.image_url,\n                                                                                alt: product.name,\n                                                                                className: \"w-10 h-10 rounded-lg object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: product.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                        lineNumber: 356,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    product.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: product.brand\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                        lineNumber: 358,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 bg-gray-100 rounded-full text-sm\",\n                                                                        children: product.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium \".concat(stockStatus.color),\n                                                                                children: [\n                                                                                    product.stock,\n                                                                                    \" \",\n                                                                                    product.unit\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 370,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            stockStatus.status !== \"good\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-orange-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(product.price_dzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: expiryStatus ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: expiryStatus.color,\n                                                                        children: expiryStatus.text\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"No expiry\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>setSelectedProduct(product),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedProduct(product);\n                                                                                    setShowAddProduct(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 407,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 399,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedProduct(product);\n                                                                                    setShowStockAdjustment(true);\n                                                                                },\n                                                                                className: \"text-orange-500 hover:text-orange-700\",\n                                                                                title: \"Adjust Stock\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 409,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleDeleteProduct(product.id),\n                                                                                className: \"text-red-500 hover:text-red-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 427,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 421,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, product.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: \"No products found matching your criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            showAddProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_add_product_modal__WEBPACK_IMPORTED_MODULE_9__.AddProductModal, {\n                product: selectedProduct,\n                onClose: ()=>{\n                    setShowAddProduct(false);\n                    setSelectedProduct(null);\n                },\n                onSave: handleProductUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 449,\n                columnNumber: 9\n            }, this),\n            showSuppliers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_suppliers_modal__WEBPACK_IMPORTED_MODULE_10__.SuppliersModal, {\n                onClose: ()=>setShowSuppliers(false),\n                onUpdate: loadData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this),\n            showPurchase && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_purchase_modal__WEBPACK_IMPORTED_MODULE_11__.PurchaseModal, {\n                onClose: ()=>setShowPurchase(false),\n                onSave: handleProductUpdate,\n                suppliers: suppliers,\n                products: products\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 467,\n                columnNumber: 9\n            }, this),\n            showStockHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_stock_movement_history__WEBPACK_IMPORTED_MODULE_12__.StockMovementHistory, {\n                onClose: ()=>setShowStockHistory(false),\n                movements: stockMovements,\n                products: products\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 476,\n                columnNumber: 9\n            }, this),\n            showStockAdjustment && selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_stock_adjustment_modal__WEBPACK_IMPORTED_MODULE_13__.StockAdjustmentModal, {\n                product: selectedProduct,\n                onClose: ()=>{\n                    setShowStockAdjustment(false);\n                    setSelectedProduct(null);\n                },\n                onSave: handleProductUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 484,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryPage, \"CuwHrfkwCWf4b9c57Zl7a6ucSdk=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvaW52ZW50b3J5L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNpQjtBQUNtQjtBQUNoQztBQUNLO0FBQ1I7QUFDd0Q7QUFDeEQ7QUFtQnZCO0FBRXJCLHlDQUF5QztBQUNpQztBQUNIO0FBQ0Y7QUFDZTtBQUNBO0FBR3JFLFNBQVNpQzs7SUFDdEIsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBRzFCLGtFQUFXQTtJQUN6QixNQUFNLEVBQUUyQixLQUFLLEVBQUUsR0FBRzFCLDBEQUFRQTtJQUUxQixtQkFBbUI7SUFDbkIsTUFBTSxDQUFDMkIsVUFBVUMsWUFBWSxHQUFHckMsK0NBQVFBLENBQW9CLEVBQUU7SUFDOUQsTUFBTSxDQUFDc0MsV0FBV0MsYUFBYSxHQUFHdkMsK0NBQVFBLENBQWEsRUFBRTtJQUN6RCxNQUFNLENBQUN3QyxnQkFBZ0JDLGtCQUFrQixHQUFHekMsK0NBQVFBLENBQWtCLEVBQUU7SUFDeEUsTUFBTSxDQUFDMEMsYUFBYUMsZUFBZSxHQUFHM0MsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDNEMsa0JBQWtCQyxvQkFBb0IsR0FBRzdDLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzhDLGNBQWNDLGdCQUFnQixHQUFHL0MsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDZ0QsY0FBY0MsZ0JBQWdCLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUVqRCxlQUFlO0lBQ2YsTUFBTSxDQUFDa0QsZ0JBQWdCQyxrQkFBa0IsR0FBR25ELCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ29ELGVBQWVDLGlCQUFpQixHQUFHckQsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDc0QsY0FBY0MsZ0JBQWdCLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN3RCxrQkFBa0JDLG9CQUFvQixHQUFHekQsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDMEQscUJBQXFCQyx1QkFBdUIsR0FBRzNELCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQzRELGFBQWFDLGVBQWUsR0FBRzdELCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzhELGlCQUFpQkMsbUJBQW1CLEdBQUcvRCwrQ0FBUUEsQ0FBeUI7SUFFL0UsK0JBQStCO0lBQy9CQyxnREFBU0EsQ0FBQztRQUNSK0Q7UUFDQXRELG9FQUFnQkEsQ0FBQ3VELHFCQUFxQjtJQUN4QyxHQUFHLEVBQUU7SUFFTCxNQUFNRCxXQUFXO1FBQ2YsSUFBSTtZQUNGLE1BQU1FLGVBQWV4RCxvRUFBZ0JBLENBQUN5RCxjQUFjLENBQWtCO1lBQ3RFLE1BQU1DLGdCQUFnQjFELG9FQUFnQkEsQ0FBQzJELFlBQVk7WUFDbkQsTUFBTUMsZ0JBQWdCNUQsb0VBQWdCQSxDQUFDNkQsaUJBQWlCO1lBRXhEbEMsWUFBWTZCO1lBQ1ozQixhQUFhNkIsY0FBY0ksTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxNQUFNO1lBQy9DakMsa0JBQWtCNkI7UUFDcEIsRUFBRSxPQUFPSyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DeEMsTUFBTTtnQkFDSjBDLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSw4Q0FBOEM7SUFDOUMsTUFBTUMsbUJBQW1CNUMsU0FBU29DLE1BQU0sQ0FBQ1MsQ0FBQUE7WUFHbEJBO1FBRnJCLE1BQU1DLGdCQUFnQkQsUUFBUUUsSUFBSSxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQzNDLFlBQVkwQyxXQUFXLE9BQzVESCxRQUFRSyxRQUFRLENBQUNGLFdBQVcsR0FBR0MsUUFBUSxDQUFDM0MsWUFBWTBDLFdBQVcsU0FDL0RILGlCQUFBQSxRQUFRTSxLQUFLLGNBQWJOLHFDQUFBQSxlQUFlRyxXQUFXLEdBQUdDLFFBQVEsQ0FBQzNDLFlBQVkwQyxXQUFXO1FBRWxGLE1BQU1JLGtCQUFrQjVDLHFCQUFxQixTQUFTcUMsUUFBUUssUUFBUSxLQUFLMUM7UUFFM0UsTUFBTTZDLGtCQUFrQixDQUFDM0MsZ0JBQWdCbUMsUUFBUVMsS0FBSyxJQUFJVCxRQUFRVSxTQUFTO1FBRTNFLE1BQU1DLGtCQUFrQixDQUFDNUMsZ0JBQ3ZCaUMsUUFBUVksV0FBVyxJQUNuQixJQUFJQyxLQUFLYixRQUFRWSxXQUFXLEtBQUssSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLEtBQUssS0FBSyxLQUFLLEtBQUs7UUFHN0UsT0FBT2IsaUJBQWlCTSxtQkFBb0IsRUFBQzFDLGdCQUFnQjJDLGVBQWMsS0FBTyxFQUFDekMsZ0JBQWdCNEMsZUFBYztJQUNuSDtJQUVBLHVCQUF1QjtJQUN2QixNQUFNSSxRQUFRO1FBQ1pDLGVBQWU3RCxTQUFTOEQsTUFBTTtRQUM5QkMsWUFBWS9ELFNBQVNnRSxNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsSUFBTUQsTUFBT0MsRUFBRVosS0FBSyxHQUFHWSxFQUFFQyxTQUFTLEVBQUc7UUFDdkVDLGVBQWVwRSxTQUFTb0MsTUFBTSxDQUFDOEIsQ0FBQUEsSUFBS0EsRUFBRVosS0FBSyxJQUFJWSxFQUFFWCxTQUFTLEVBQUVPLE1BQU07UUFDbEVPLGVBQWVyRSxTQUFTb0MsTUFBTSxDQUFDOEIsQ0FBQUEsSUFDN0JBLEVBQUVULFdBQVcsSUFBSSxJQUFJQyxLQUFLUSxFQUFFVCxXQUFXLEtBQUssSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLEtBQUssS0FBSyxLQUFLLEtBQUssT0FDdEZHLE1BQU07UUFDUlEsaUJBQWlCcEUsVUFBVTRELE1BQU07SUFDbkM7SUFFQSxNQUFNUyxhQUFhO1dBQUksSUFBSUMsSUFBSXhFLFNBQVN5RSxHQUFHLENBQUNQLENBQUFBLElBQUtBLEVBQUVoQixRQUFRO0tBQUc7SUFFOUQsTUFBTXdCLHNCQUFzQjtRQUMxQjlDO1FBQ0FELG1CQUFtQjtJQUNyQjtJQUVBLE1BQU1nRCxzQkFBc0IsQ0FBQ0M7UUFDM0IsSUFBSUMsUUFBUSxrREFBa0Q7WUFDNUQsTUFBTUMsa0JBQWtCOUUsU0FBU29DLE1BQU0sQ0FBQzhCLENBQUFBLElBQUtBLEVBQUVhLEVBQUUsS0FBS0g7WUFDdER0RyxvRUFBZ0JBLENBQUMwRyxhQUFhLENBQUMsZ0JBQWdCRjtZQUMvQ2xEO1lBQ0E3QixNQUFNO2dCQUNKMEMsT0FBTztnQkFDUEMsYUFBYTtZQUNmO1FBQ0Y7SUFDRjtJQUVBLE1BQU11QyxpQkFBaUIsQ0FBQ3BDO1FBQ3RCLElBQUlBLFFBQVFTLEtBQUssS0FBSyxHQUFHLE9BQU87WUFBRTRCLFFBQVE7WUFBT0MsT0FBTztZQUFnQkMsSUFBSTtRQUFZO1FBQ3hGLElBQUl2QyxRQUFRUyxLQUFLLElBQUlULFFBQVFVLFNBQVMsRUFBRSxPQUFPO1lBQUUyQixRQUFRO1lBQU9DLE9BQU87WUFBbUJDLElBQUk7UUFBZTtRQUM3RyxPQUFPO1lBQUVGLFFBQVE7WUFBUUMsT0FBTztZQUFrQkMsSUFBSTtRQUFjO0lBQ3RFO0lBRUEsTUFBTUMsa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUksQ0FBQ0EsWUFBWSxPQUFPO1FBRXhCLE1BQU1DLFFBQVEsSUFBSTdCO1FBQ2xCLE1BQU04QixTQUFTLElBQUk5QixLQUFLNEI7UUFDeEIsTUFBTUcsa0JBQWtCQyxLQUFLQyxJQUFJLENBQUMsQ0FBQ0gsT0FBT0ksT0FBTyxLQUFLTCxNQUFNSyxPQUFPLEVBQUMsSUFBTSxRQUFPLEtBQUssS0FBSyxFQUFDO1FBRTVGLElBQUlILGtCQUFrQixHQUFHLE9BQU87WUFBRVAsUUFBUTtZQUFXQyxPQUFPO1lBQWdCVSxNQUFNO1FBQVU7UUFDNUYsSUFBSUosbUJBQW1CLEdBQUcsT0FBTztZQUFFUCxRQUFRO1lBQVlDLE9BQU87WUFBbUJVLE1BQU0sR0FBbUIsT0FBaEJKLGlCQUFnQjtRQUFPO1FBQ2pILElBQUlBLG1CQUFtQixJQUFJLE9BQU87WUFBRVAsUUFBUTtZQUFXQyxPQUFPO1lBQW1CVSxNQUFNLEdBQW1CLE9BQWhCSixpQkFBZ0I7UUFBTztRQUNqSCxPQUFPO1lBQUVQLFFBQVE7WUFBUUMsT0FBTztZQUFrQlUsTUFBTSxHQUFtQixPQUFoQkosaUJBQWdCO1FBQU87SUFDcEY7SUFFQSxxQkFDRSw4REFBQzNILHNFQUFVQTtRQUFDMkUsT0FBTTs7MEJBQ2hCLDhEQUFDcUQ7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNoSSxxREFBSUE7Z0NBQUNnSSxXQUFVOzBDQUNkLDRFQUFDL0gsNERBQVdBO29DQUFDK0gsV0FBVTs4Q0FDckIsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3ZILDBNQUFPQTtnREFBQ3VILFdBQVU7Ozs7OzswREFDbkIsOERBQUNEOztrRUFDQyw4REFBQzVCO3dEQUFFNkIsV0FBVTtrRUFBd0I7Ozs7OztrRUFDckMsOERBQUM3Qjt3REFBRTZCLFdBQVU7a0VBQXNCbkMsTUFBTUMsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNOUQsOERBQUM5RixxREFBSUE7Z0NBQUNnSSxXQUFVOzBDQUNkLDRFQUFDL0gsNERBQVdBO29DQUFDK0gsV0FBVTs4Q0FDckIsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3BILDBNQUFVQTtnREFBQ29ILFdBQVU7Ozs7OzswREFDdEIsOERBQUNEOztrRUFDQyw4REFBQzVCO3dEQUFFNkIsV0FBVTtrRUFBd0I7Ozs7OztrRUFDckMsOERBQUM3Qjt3REFBRTZCLFdBQVU7a0VBQXNCeEgsMERBQWNBLENBQUNxRixNQUFNRyxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU0xRSw4REFBQ2hHLHFEQUFJQTtnQ0FBQ2dJLFdBQVU7MENBQ2QsNEVBQUMvSCw0REFBV0E7b0NBQUMrSCxXQUFVOzhDQUNyQiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDckgsME1BQWFBO2dEQUFDcUgsV0FBVTs7Ozs7OzBEQUN6Qiw4REFBQ0Q7O2tFQUNDLDhEQUFDNUI7d0RBQUU2QixXQUFVO2tFQUF3Qjs7Ozs7O2tFQUNyQyw4REFBQzdCO3dEQUFFNkIsV0FBVTtrRUFBc0JuQyxNQUFNUSxhQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU05RCw4REFBQ3JHLHFEQUFJQTtnQ0FBQ2dJLFdBQVU7MENBQ2QsNEVBQUMvSCw0REFBV0E7b0NBQUMrSCxXQUFVOzhDQUNyQiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDaEgsME1BQVFBO2dEQUFDZ0gsV0FBVTs7Ozs7OzBEQUNwQiw4REFBQ0Q7O2tFQUNDLDhEQUFDNUI7d0RBQUU2QixXQUFVO2tFQUF3Qjs7Ozs7O2tFQUNyQyw4REFBQzdCO3dEQUFFNkIsV0FBVTtrRUFBc0JuQyxNQUFNUyxhQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU05RCw4REFBQ3RHLHFEQUFJQTtnQ0FBQ2dJLFdBQVU7MENBQ2QsNEVBQUMvSCw0REFBV0E7b0NBQUMrSCxXQUFVOzhDQUNyQiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDbEgsME1BQUtBO2dEQUFDa0gsV0FBVTs7Ozs7OzBEQUNqQiw4REFBQ0Q7O2tFQUNDLDhEQUFDNUI7d0RBQUU2QixXQUFVO2tFQUF3Qjs7Ozs7O2tFQUNyQyw4REFBQzdCO3dEQUFFNkIsV0FBVTtrRUFBc0JuQyxNQUFNVSxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVFsRSw4REFBQ3dCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzVILHlEQUFNQTtnQ0FDTDZILFNBQVMsSUFBTWpGLGtCQUFrQjtnQ0FDakNnRixXQUFVOztrREFFViw4REFBQ3RILDBNQUFJQTt3Q0FBQ3NILFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7MENBSW5DLDhEQUFDNUgseURBQU1BO2dDQUNMNkgsU0FBUyxJQUFNN0UsZ0JBQWdCO2dDQUMvQjRFLFdBQVU7O2tEQUVWLDhEQUFDakgsME1BQVlBO3dDQUFDaUgsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FJM0MsOERBQUM1SCx5REFBTUE7Z0NBQ0w2SCxTQUFTLElBQU0vRSxpQkFBaUI7Z0NBQ2hDOEUsV0FBVTs7a0RBRVYsOERBQUNsSCwwTUFBS0E7d0NBQUNrSCxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUlwQyw4REFBQzVILHlEQUFNQTtnQ0FDTDZILFNBQVMsSUFBTTNFLG9CQUFvQjtnQ0FDbkNzQixTQUFRO2dDQUNSb0QsV0FBVTs7a0RBRVYsOERBQUNuSCwwTUFBWUE7d0NBQUNtSCxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUkzQyw4REFBQzVILHlEQUFNQTtnQ0FDTDZILFNBQVMsSUFBTXZFLGVBQWU7Z0NBQzlCa0IsU0FBUTtnQ0FDUm9ELFdBQVU7O2tEQUVWLDhEQUFDeEcsME1BQVFBO3dDQUFDd0csV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7OztrQ0FNekMsOERBQUNoSSxxREFBSUE7d0JBQUNnSSxXQUFVO2tDQUNkLDRFQUFDL0gsNERBQVdBOzRCQUFDK0gsV0FBVTtzQ0FDckIsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDL0csME1BQU1BO2dEQUFDK0csV0FBVTs7Ozs7OzBEQUNsQiw4REFBQ0U7Z0RBQ0NDLE1BQUs7Z0RBQ0xDLGFBQVk7Z0RBQ1pDLE9BQU85RjtnREFDUCtGLFVBQVUsQ0FBQ0MsSUFBTS9GLGVBQWUrRixFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0RBQzlDTCxXQUFVOzs7Ozs7Ozs7Ozs7a0RBSWQsOERBQUNTO3dDQUNDSixPQUFPNUY7d0NBQ1A2RixVQUFVLENBQUNDLElBQU03RixvQkFBb0I2RixFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQ25ETCxXQUFVOzswREFFViw4REFBQ1U7Z0RBQU9MLE9BQU07MERBQU07Ozs7Ozs0Q0FDbkI3QixXQUFXRSxHQUFHLENBQUN2QixDQUFBQSx5QkFDZCw4REFBQ3VEO29EQUFzQkwsT0FBT2xEOzhEQUFXQTttREFBNUJBOzs7Ozs7Ozs7OztrREFJakIsOERBQUMvRSx5REFBTUE7d0NBQ0x3RSxTQUFTakMsZUFBZSxZQUFZO3dDQUNwQ3NGLFNBQVMsSUFBTXJGLGdCQUFnQixDQUFDRDt3Q0FDaENnRyxNQUFLOzswREFFTCw4REFBQ2hJLDBNQUFhQTtnREFBQ3FILFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7a0RBSTVDLDhEQUFDNUgseURBQU1BO3dDQUNMd0UsU0FBUy9CLGVBQWUsWUFBWTt3Q0FDcENvRixTQUFTLElBQU1uRixnQkFBZ0IsQ0FBQ0Q7d0NBQ2hDOEYsTUFBSzs7MERBRUwsOERBQUMzSCwwTUFBUUE7Z0RBQUNnSCxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRN0MsOERBQUNoSSxxREFBSUE7d0JBQUNnSSxXQUFVOzswQ0FDZCw4REFBQzlILDJEQUFVQTswQ0FDVCw0RUFBQ0MsMERBQVNBO29DQUFDNkgsV0FBVTs7c0RBQ25CLDhEQUFDWTs7Z0RBQUs7Z0RBQVcvRCxpQkFBaUJrQixNQUFNO2dEQUFDOzs7Ozs7O3NEQUN6Qyw4REFBQzNGLHlEQUFNQTs0Q0FBQ3dFLFNBQVE7NENBQVUrRCxNQUFLOzs4REFDN0IsOERBQUN6SCwwTUFBUUE7b0RBQUM4RyxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSzNDLDhEQUFDL0gsNERBQVdBOzBDQUNWLDRFQUFDOEg7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDYTs0Q0FBTWIsV0FBVTs7OERBQ2YsOERBQUNjOzhEQUNDLDRFQUFDQzt3REFBR2YsV0FBVTs7MEVBQ1osOERBQUNnQjtnRUFBR2hCLFdBQVU7MEVBQXNCOzs7Ozs7MEVBQ3BDLDhEQUFDZ0I7Z0VBQUdoQixXQUFVOzBFQUFzQjs7Ozs7OzBFQUNwQyw4REFBQ2dCO2dFQUFHaEIsV0FBVTswRUFBc0I7Ozs7OzswRUFDcEMsOERBQUNnQjtnRUFBR2hCLFdBQVU7MEVBQXNCOzs7Ozs7MEVBQ3BDLDhEQUFDZ0I7Z0VBQUdoQixXQUFVOzBFQUFzQjs7Ozs7OzBFQUNwQyw4REFBQ2dCO2dFQUFHaEIsV0FBVTswRUFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUd4Qyw4REFBQ2lCOzhEQUNFcEUsaUJBQWlCNkIsR0FBRyxDQUFDLENBQUM1Qjt3REFDckIsTUFBTW9FLGNBQWNoQyxlQUFlcEM7d0RBQ25DLE1BQU1xRSxlQUFlN0IsZ0JBQWdCeEMsUUFBUVksV0FBVzt3REFFeEQscUJBQ0UsOERBQUNxRDs0REFBb0JmLFdBQVU7OzhFQUM3Qiw4REFBQ29CO29FQUFHcEIsV0FBVTs4RUFDWiw0RUFBQ0Q7d0VBQUlDLFdBQVU7OzRFQUNabEQsUUFBUXVFLFNBQVMsaUJBQ2hCLDhEQUFDQztnRkFDQ0MsS0FBS3pFLFFBQVF1RSxTQUFTO2dGQUN0QkcsS0FBSzFFLFFBQVFFLElBQUk7Z0ZBQ2pCZ0QsV0FBVTs7Ozs7cUdBR1osOERBQUNEO2dGQUFJQyxXQUFVOzBGQUNiLDRFQUFDekcsME1BQVNBO29GQUFDeUcsV0FBVTs7Ozs7Ozs7Ozs7MEZBR3pCLDhEQUFDRDs7a0dBQ0MsOERBQUM1Qjt3RkFBRTZCLFdBQVU7a0dBQWVsRCxRQUFRRSxJQUFJOzs7Ozs7b0ZBQ3ZDRixRQUFRTSxLQUFLLGtCQUNaLDhEQUFDZTt3RkFBRTZCLFdBQVU7a0dBQXlCbEQsUUFBUU0sS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBSzNELDhEQUFDZ0U7b0VBQUdwQixXQUFVOzhFQUNaLDRFQUFDWTt3RUFBS1osV0FBVTtrRkFDYmxELFFBQVFLLFFBQVE7Ozs7Ozs7Ozs7OzhFQUdyQiw4REFBQ2lFO29FQUFHcEIsV0FBVTs4RUFDWiw0RUFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDWTtnRkFBS1osV0FBVyxlQUFpQyxPQUFsQmtCLFlBQVk5QixLQUFLOztvRkFDOUN0QyxRQUFRUyxLQUFLO29GQUFDO29GQUFFVCxRQUFRMkUsSUFBSTs7Ozs7Ozs0RUFFOUJQLFlBQVkvQixNQUFNLEtBQUssd0JBQ3RCLDhEQUFDeEcsME1BQWFBO2dGQUFDcUgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBSS9CLDhEQUFDb0I7b0VBQUdwQixXQUFVOzhFQUNYeEgsMERBQWNBLENBQUNzRSxRQUFRc0IsU0FBUzs7Ozs7OzhFQUVuQyw4REFBQ2dEO29FQUFHcEIsV0FBVTs4RUFDWG1CLDZCQUNDLDhEQUFDUDt3RUFBS1osV0FBV21CLGFBQWEvQixLQUFLO2tGQUNoQytCLGFBQWFyQixJQUFJOzs7Ozs2RkFHcEIsOERBQUNjO3dFQUFLWixXQUFVO2tGQUFnQjs7Ozs7Ozs7Ozs7OEVBR3BDLDhEQUFDb0I7b0VBQUdwQixXQUFVOzhFQUNaLDRFQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUM1SCx5REFBTUE7Z0ZBQ0x3RSxTQUFRO2dGQUNSK0QsTUFBSztnRkFDTFYsU0FBUyxJQUFNckUsbUJBQW1Ca0I7MEZBRWxDLDRFQUFDM0QsME1BQUdBO29GQUFDNkcsV0FBVTs7Ozs7Ozs7Ozs7MEZBRWpCLDhEQUFDNUgseURBQU1BO2dGQUNMd0UsU0FBUTtnRkFDUitELE1BQUs7Z0ZBQ0xWLFNBQVM7b0ZBQ1ByRSxtQkFBbUJrQjtvRkFDbkI5QixrQkFBa0I7Z0ZBQ3BCOzBGQUVBLDRFQUFDNUIsME1BQUlBO29GQUFDNEcsV0FBVTs7Ozs7Ozs7Ozs7MEZBRWxCLDhEQUFDNUgseURBQU1BO2dGQUNMd0UsU0FBUTtnRkFDUitELE1BQUs7Z0ZBQ0xWLFNBQVM7b0ZBQ1ByRSxtQkFBbUJrQjtvRkFDbkJ0Qix1QkFBdUI7Z0ZBQ3pCO2dGQUNBd0UsV0FBVTtnRkFDVnRELE9BQU07MEZBRU4sNEVBQUM5RCwwTUFBVUE7b0ZBQUNvSCxXQUFVOzs7Ozs7Ozs7OzswRkFFeEIsOERBQUM1SCx5REFBTUE7Z0ZBQ0x3RSxTQUFRO2dGQUNSK0QsTUFBSztnRkFDTFYsU0FBUyxJQUFNckIsb0JBQW9COUIsUUFBUWtDLEVBQUU7Z0ZBQzdDZ0IsV0FBVTswRkFFViw0RUFBQzNHLDBNQUFNQTtvRkFBQzJHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJEQXRGakJsRCxRQUFRa0MsRUFBRTs7Ozs7b0RBNEZ2Qjs7Ozs7Ozs7Ozs7O3dDQUlIbkMsaUJBQWlCa0IsTUFBTSxLQUFLLG1CQUMzQiw4REFBQ2dDOzRDQUFJQyxXQUFVO3NEQUFpQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFVekRqRixnQ0FDQyw4REFBQ3RCLG9GQUFlQTtnQkFDZHFELFNBQVNuQjtnQkFDVCtGLFNBQVM7b0JBQ1AxRyxrQkFBa0I7b0JBQ2xCWSxtQkFBbUI7Z0JBQ3JCO2dCQUNBK0YsUUFBUWhEOzs7Ozs7WUFJWDFELCtCQUNDLDhEQUFDdkIsa0ZBQWNBO2dCQUNiZ0ksU0FBUyxJQUFNeEcsaUJBQWlCO2dCQUNoQzBHLFVBQVUvRjs7Ozs7O1lBSWJWLDhCQUNDLDhEQUFDeEIsZ0ZBQWFBO2dCQUNaK0gsU0FBUyxJQUFNdEcsZ0JBQWdCO2dCQUMvQnVHLFFBQVFoRDtnQkFDUnhFLFdBQVdBO2dCQUNYRixVQUFVQTs7Ozs7O1lBSWJvQixrQ0FDQyw4REFBQ3pCLCtGQUFvQkE7Z0JBQ25COEgsU0FBUyxJQUFNcEcsb0JBQW9CO2dCQUNuQ3VHLFdBQVd4SDtnQkFDWEosVUFBVUE7Ozs7OztZQUlic0IsdUJBQXVCSSxpQ0FDdEIsOERBQUM5QiwrRkFBb0JBO2dCQUNuQmlELFNBQVNuQjtnQkFDVCtGLFNBQVM7b0JBQ1BsRyx1QkFBdUI7b0JBQ3ZCSSxtQkFBbUI7Z0JBQ3JCO2dCQUNBK0YsUUFBUWhEOzs7Ozs7Ozs7Ozs7QUFLbEI7R0F4Y3dCN0U7O1FBQ1J6Qiw4REFBV0E7UUFDUEMsc0RBQVFBOzs7S0FGSndCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvaW52ZW50b3J5L3BhZ2UudHN4PzJiYTciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IE1haW5MYXlvdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L21haW4tbGF5b3V0J1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IHVzZUxhbmd1YWdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3Byb3ZpZGVycydcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9ob29rcy91c2UtdG9hc3QnXG5pbXBvcnQgeyBJbnZlbnRvcnlTdG9yYWdlLCBFeHRlbmRlZFByb2R1Y3QsIFN1cHBsaWVyLCBTdG9ja01vdmVtZW50IH0gZnJvbSAnQC9saWIvaW52ZW50b3J5LXN0b3JhZ2UnXG5pbXBvcnQgeyBmb3JtYXRDdXJyZW5jeSB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuaW1wb3J0IHtcbiAgUGFja2FnZSxcbiAgUGx1cyxcbiAgQWxlcnRUcmlhbmdsZSxcbiAgVHJlbmRpbmdVcCxcbiAgVHJlbmRpbmdEb3duLFxuICBVc2VycyxcbiAgU2hvcHBpbmdDYXJ0LFxuICBDYWxlbmRhcixcbiAgU2VhcmNoLFxuICBGaWx0ZXIsXG4gIERvd25sb2FkLFxuICBFeWUsXG4gIEVkaXQsXG4gIFRyYXNoMixcbiAgU2NhbixcbiAgSW1hZ2UgYXMgSW1hZ2VJY29uLFxuICBGaWxlVGV4dCxcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG4vLyBJbXBvcnQgY29tcG9uZW50cyAod2UnbGwgY3JlYXRlIHRoZXNlKVxuaW1wb3J0IHsgQWRkUHJvZHVjdE1vZGFsIH0gZnJvbSAnQC9jb21wb25lbnRzL2ludmVudG9yeS9hZGQtcHJvZHVjdC1tb2RhbCdcbmltcG9ydCB7IFN1cHBsaWVyc01vZGFsIH0gZnJvbSAnQC9jb21wb25lbnRzL2ludmVudG9yeS9zdXBwbGllcnMtbW9kYWwnXG5pbXBvcnQgeyBQdXJjaGFzZU1vZGFsIH0gZnJvbSAnQC9jb21wb25lbnRzL2ludmVudG9yeS9wdXJjaGFzZS1tb2RhbCdcbmltcG9ydCB7IFN0b2NrTW92ZW1lbnRIaXN0b3J5IH0gZnJvbSAnQC9jb21wb25lbnRzL2ludmVudG9yeS9zdG9jay1tb3ZlbWVudC1oaXN0b3J5J1xuaW1wb3J0IHsgU3RvY2tBZGp1c3RtZW50TW9kYWwgfSBmcm9tICdAL2NvbXBvbmVudHMvaW52ZW50b3J5L3N0b2NrLWFkanVzdG1lbnQtbW9kYWwnXG5pbXBvcnQgeyBJbnZlbnRvcnlSZXBvcnRzIH0gZnJvbSAnQC9jb21wb25lbnRzL2ludmVudG9yeS9pbnZlbnRvcnktcmVwb3J0cydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSW52ZW50b3J5UGFnZSgpIHtcbiAgY29uc3QgeyB0IH0gPSB1c2VMYW5ndWFnZSgpXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcblxuICAvLyBTdGF0ZSBtYW5hZ2VtZW50XG4gIGNvbnN0IFtwcm9kdWN0cywgc2V0UHJvZHVjdHNdID0gdXNlU3RhdGU8RXh0ZW5kZWRQcm9kdWN0W10+KFtdKVxuICBjb25zdCBbc3VwcGxpZXJzLCBzZXRTdXBwbGllcnNdID0gdXNlU3RhdGU8U3VwcGxpZXJbXT4oW10pXG4gIGNvbnN0IFtzdG9ja01vdmVtZW50cywgc2V0U3RvY2tNb3ZlbWVudHNdID0gdXNlU3RhdGU8U3RvY2tNb3ZlbWVudFtdPihbXSlcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGUoJ2FsbCcpXG4gIGNvbnN0IFtzaG93TG93U3RvY2ssIHNldFNob3dMb3dTdG9ja10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Nob3dFeHBpcmluZywgc2V0U2hvd0V4cGlyaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIC8vIE1vZGFsIHN0YXRlc1xuICBjb25zdCBbc2hvd0FkZFByb2R1Y3QsIHNldFNob3dBZGRQcm9kdWN0XSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd1N1cHBsaWVycywgc2V0U2hvd1N1cHBsaWVyc10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Nob3dQdXJjaGFzZSwgc2V0U2hvd1B1cmNoYXNlXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd1N0b2NrSGlzdG9yeSwgc2V0U2hvd1N0b2NrSGlzdG9yeV0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Nob3dTdG9ja0FkanVzdG1lbnQsIHNldFNob3dTdG9ja0FkanVzdG1lbnRdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzaG93UmVwb3J0cywgc2V0U2hvd1JlcG9ydHNdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZWxlY3RlZFByb2R1Y3QsIHNldFNlbGVjdGVkUHJvZHVjdF0gPSB1c2VTdGF0ZTxFeHRlbmRlZFByb2R1Y3QgfCBudWxsPihudWxsKVxuXG4gIC8vIExvYWQgZGF0YSBvbiBjb21wb25lbnQgbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkRGF0YSgpXG4gICAgSW52ZW50b3J5U3RvcmFnZS5pbml0aWFsaXplRGVmYXVsdERhdGEoKVxuICB9LCBbXSlcblxuICBjb25zdCBsb2FkRGF0YSA9ICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcHJvZHVjdHNEYXRhID0gSW52ZW50b3J5U3RvcmFnZS5nZXRGcm9tU3RvcmFnZTxFeHRlbmRlZFByb2R1Y3Q+KCdneW1fcHJvZHVjdHMnKVxuICAgICAgY29uc3Qgc3VwcGxpZXJzRGF0YSA9IEludmVudG9yeVN0b3JhZ2UuZ2V0U3VwcGxpZXJzKClcbiAgICAgIGNvbnN0IG1vdmVtZW50c0RhdGEgPSBJbnZlbnRvcnlTdG9yYWdlLmdldFN0b2NrTW92ZW1lbnRzKClcblxuICAgICAgc2V0UHJvZHVjdHMocHJvZHVjdHNEYXRhKVxuICAgICAgc2V0U3VwcGxpZXJzKHN1cHBsaWVyc0RhdGEuZmlsdGVyKHMgPT4gcy5hY3RpdmUpKVxuICAgICAgc2V0U3RvY2tNb3ZlbWVudHMobW92ZW1lbnRzRGF0YSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBpbnZlbnRvcnkgZGF0YTonLCBlcnJvcilcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRmFpbGVkIHRvIGxvYWQgaW52ZW50b3J5IGRhdGEnLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICAvLyBGaWx0ZXIgcHJvZHVjdHMgYmFzZWQgb24gc2VhcmNoIGFuZCBmaWx0ZXJzXG4gIGNvbnN0IGZpbHRlcmVkUHJvZHVjdHMgPSBwcm9kdWN0cy5maWx0ZXIocHJvZHVjdCA9PiB7XG4gICAgY29uc3QgbWF0Y2hlc1NlYXJjaCA9IHByb2R1Y3QubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgcHJvZHVjdC5jYXRlZ29yeS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgcHJvZHVjdC5icmFuZD8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKVxuICAgIFxuICAgIGNvbnN0IG1hdGNoZXNDYXRlZ29yeSA9IHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdhbGwnIHx8IHByb2R1Y3QuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnlcbiAgICBcbiAgICBjb25zdCBtYXRjaGVzTG93U3RvY2sgPSAhc2hvd0xvd1N0b2NrIHx8IHByb2R1Y3Quc3RvY2sgPD0gcHJvZHVjdC5taW5fc3RvY2tcbiAgICBcbiAgICBjb25zdCBtYXRjaGVzRXhwaXJpbmcgPSAhc2hvd0V4cGlyaW5nIHx8IChcbiAgICAgIHByb2R1Y3QuZXhwaXJ5X2RhdGUgJiYgXG4gICAgICBuZXcgRGF0ZShwcm9kdWN0LmV4cGlyeV9kYXRlKSA8PSBuZXcgRGF0ZShEYXRlLm5vdygpICsgMzAgKiAyNCAqIDYwICogNjAgKiAxMDAwKVxuICAgIClcblxuICAgIHJldHVybiBtYXRjaGVzU2VhcmNoICYmIG1hdGNoZXNDYXRlZ29yeSAmJiAoIXNob3dMb3dTdG9jayB8fCBtYXRjaGVzTG93U3RvY2spICYmICghc2hvd0V4cGlyaW5nIHx8IG1hdGNoZXNFeHBpcmluZylcbiAgfSlcblxuICAvLyBDYWxjdWxhdGUgc3RhdGlzdGljc1xuICBjb25zdCBzdGF0cyA9IHtcbiAgICB0b3RhbFByb2R1Y3RzOiBwcm9kdWN0cy5sZW5ndGgsXG4gICAgdG90YWxWYWx1ZTogcHJvZHVjdHMucmVkdWNlKChzdW0sIHApID0+IHN1bSArIChwLnN0b2NrICogcC5wcmljZV9kemQpLCAwKSxcbiAgICBsb3dTdG9ja0NvdW50OiBwcm9kdWN0cy5maWx0ZXIocCA9PiBwLnN0b2NrIDw9IHAubWluX3N0b2NrKS5sZW5ndGgsXG4gICAgZXhwaXJpbmdDb3VudDogcHJvZHVjdHMuZmlsdGVyKHAgPT4gXG4gICAgICBwLmV4cGlyeV9kYXRlICYmIG5ldyBEYXRlKHAuZXhwaXJ5X2RhdGUpIDw9IG5ldyBEYXRlKERhdGUubm93KCkgKyAzMCAqIDI0ICogNjAgKiA2MCAqIDEwMDApXG4gICAgKS5sZW5ndGgsXG4gICAgYWN0aXZlU3VwcGxpZXJzOiBzdXBwbGllcnMubGVuZ3RoLFxuICB9XG5cbiAgY29uc3QgY2F0ZWdvcmllcyA9IFsuLi5uZXcgU2V0KHByb2R1Y3RzLm1hcChwID0+IHAuY2F0ZWdvcnkpKV1cblxuICBjb25zdCBoYW5kbGVQcm9kdWN0VXBkYXRlID0gKCkgPT4ge1xuICAgIGxvYWREYXRhKClcbiAgICBzZXRTZWxlY3RlZFByb2R1Y3QobnVsbClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZVByb2R1Y3QgPSAocHJvZHVjdElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoY29uZmlybSgnQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIHByb2R1Y3Q/JykpIHtcbiAgICAgIGNvbnN0IHVwZGF0ZWRQcm9kdWN0cyA9IHByb2R1Y3RzLmZpbHRlcihwID0+IHAuaWQgIT09IHByb2R1Y3RJZClcbiAgICAgIEludmVudG9yeVN0b3JhZ2Uuc2F2ZVRvU3RvcmFnZSgnZ3ltX3Byb2R1Y3RzJywgdXBkYXRlZFByb2R1Y3RzKVxuICAgICAgbG9hZERhdGEoKVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ1Byb2R1Y3QgZGVsZXRlZCBzdWNjZXNzZnVsbHknLFxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdG9ja1N0YXR1cyA9IChwcm9kdWN0OiBFeHRlbmRlZFByb2R1Y3QpID0+IHtcbiAgICBpZiAocHJvZHVjdC5zdG9jayA9PT0gMCkgcmV0dXJuIHsgc3RhdHVzOiAnb3V0JywgY29sb3I6ICd0ZXh0LXJlZC01MDAnLCBiZzogJ2JnLXJlZC01MCcgfVxuICAgIGlmIChwcm9kdWN0LnN0b2NrIDw9IHByb2R1Y3QubWluX3N0b2NrKSByZXR1cm4geyBzdGF0dXM6ICdsb3cnLCBjb2xvcjogJ3RleHQtb3JhbmdlLTUwMCcsIGJnOiAnYmctb3JhbmdlLTUwJyB9XG4gICAgcmV0dXJuIHsgc3RhdHVzOiAnZ29vZCcsIGNvbG9yOiAndGV4dC1ncmVlbi01MDAnLCBiZzogJ2JnLWdyZWVuLTUwJyB9XG4gIH1cblxuICBjb25zdCBnZXRFeHBpcnlTdGF0dXMgPSAoZXhwaXJ5RGF0ZT86IHN0cmluZykgPT4ge1xuICAgIGlmICghZXhwaXJ5RGF0ZSkgcmV0dXJuIG51bGxcbiAgICBcbiAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKClcbiAgICBjb25zdCBleHBpcnkgPSBuZXcgRGF0ZShleHBpcnlEYXRlKVxuICAgIGNvbnN0IGRheXNVbnRpbEV4cGlyeSA9IE1hdGguY2VpbCgoZXhwaXJ5LmdldFRpbWUoKSAtIHRvZGF5LmdldFRpbWUoKSkgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpXG4gICAgXG4gICAgaWYgKGRheXNVbnRpbEV4cGlyeSA8IDApIHJldHVybiB7IHN0YXR1czogJ2V4cGlyZWQnLCBjb2xvcjogJ3RleHQtcmVkLTUwMCcsIHRleHQ6ICdFeHBpcmVkJyB9XG4gICAgaWYgKGRheXNVbnRpbEV4cGlyeSA8PSA3KSByZXR1cm4geyBzdGF0dXM6ICdleHBpcmluZycsIGNvbG9yOiAndGV4dC1vcmFuZ2UtNTAwJywgdGV4dDogYCR7ZGF5c1VudGlsRXhwaXJ5fSBkYXlzYCB9XG4gICAgaWYgKGRheXNVbnRpbEV4cGlyeSA8PSAzMCkgcmV0dXJuIHsgc3RhdHVzOiAnd2FybmluZycsIGNvbG9yOiAndGV4dC15ZWxsb3ctNTAwJywgdGV4dDogYCR7ZGF5c1VudGlsRXhwaXJ5fSBkYXlzYCB9XG4gICAgcmV0dXJuIHsgc3RhdHVzOiAnZ29vZCcsIGNvbG9yOiAndGV4dC1ncmVlbi01MDAnLCB0ZXh0OiBgJHtkYXlzVW50aWxFeHBpcnl9IGRheXNgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPE1haW5MYXlvdXQgdGl0bGU9XCJJbnZlbnRvcnkgTWFuYWdlbWVudFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgey8qIFN0YXRpc3RpY3MgQ2FyZHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNSBnYXAtNFwiPlxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImdsYXNzIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1ibHVlLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlRvdGFsIFByb2R1Y3RzPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e3N0YXRzLnRvdGFsUHJvZHVjdHN9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiZ2xhc3MgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlRvdGFsIFZhbHVlPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e2Zvcm1hdEN1cnJlbmN5KHN0YXRzLnRvdGFsVmFsdWUpfTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImdsYXNzIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1vcmFuZ2UtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+TG93IFN0b2NrPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e3N0YXRzLmxvd1N0b2NrQ291bnR9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiZ2xhc3MgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+RXhwaXJpbmcgU29vbjwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntzdGF0cy5leHBpcmluZ0NvdW50fTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImdsYXNzIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtcHVycGxlLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlN1cHBsaWVyczwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntzdGF0cy5hY3RpdmVTdXBwbGllcnN9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTRcIj5cbiAgICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0FkZFByb2R1Y3QodHJ1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgQWRkIE5ldyBQcm9kdWN0XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQdXJjaGFzZSh0cnVlKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwMCBob3ZlcjpiZy1ncmVlbi02MDAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNob3BwaW5nQ2FydCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgTmV3IFB1cmNoYXNlXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dTdXBwbGllcnModHJ1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNTAwIGhvdmVyOmJnLXB1cnBsZS02MDAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICBNYW5hZ2UgU3VwcGxpZXJzXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1N0b2NrSGlzdG9yeSh0cnVlKX1cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTMwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFRyZW5kaW5nRG93biBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgU3RvY2sgSGlzdG9yeVxuICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1JlcG9ydHModHJ1ZSl9XG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS0zMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgUmVwb3J0c1xuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU2VhcmNoIGFuZCBGaWx0ZXJzICovfVxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJnbGFzcyBib3JkZXItd2hpdGUvMjBcIj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC00IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgtMSBtaW4tdy02NFwiPlxuICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC1ncmF5LTQwMCB3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIHByb2R1Y3RzLi4uXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hRdWVyeX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHBsLTEwIHByLTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRDYXRlZ29yeX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkQ2F0ZWdvcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFsbFwiPkFsbCBDYXRlZ29yaWVzPC9vcHRpb24+XG4gICAgICAgICAgICAgICAge2NhdGVnb3JpZXMubWFwKGNhdGVnb3J5ID0+IChcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjYXRlZ29yeX0gdmFsdWU9e2NhdGVnb3J5fT57Y2F0ZWdvcnl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9e3Nob3dMb3dTdG9jayA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0xvd1N0b2NrKCFzaG93TG93U3RvY2spfVxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIExvdyBTdG9ja1xuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PXtzaG93RXhwaXJpbmcgPyBcImRlZmF1bHRcIiA6IFwib3V0bGluZVwifVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dFeHBpcmluZyghc2hvd0V4cGlyaW5nKX1cbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgRXhwaXJpbmdcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIFByb2R1Y3RzIFRhYmxlICovfVxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJnbGFzcyBib3JkZXItd2hpdGUvMjBcIj5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxzcGFuPlByb2R1Y3RzICh7ZmlsdGVyZWRQcm9kdWN0cy5sZW5ndGh9KTwvc3Bhbj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIEV4cG9ydFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgPHRoZWFkPlxuICAgICAgICAgICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgcHgtNFwiPlByb2R1Y3Q8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgcHgtNFwiPkNhdGVnb3J5PC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtbGVmdCBweS0zIHB4LTRcIj5TdG9jazwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00XCI+UHJpY2U8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHB5LTMgcHgtNFwiPkV4cGlyeTwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LWxlZnQgcHktMyBweC00XCI+QWN0aW9uczwvdGg+XG4gICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgPHRib2R5PlxuICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkUHJvZHVjdHMubWFwKChwcm9kdWN0KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHN0b2NrU3RhdHVzID0gZ2V0U3RvY2tTdGF0dXMocHJvZHVjdClcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhwaXJ5U3RhdHVzID0gZ2V0RXhwaXJ5U3RhdHVzKHByb2R1Y3QuZXhwaXJ5X2RhdGUpXG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e3Byb2R1Y3QuaWR9IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3QuaW1hZ2VfdXJsID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtwcm9kdWN0LmltYWdlX3VybH0gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17cHJvZHVjdC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgcm91bmRlZC1sZyBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctZ3JheS0yMDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2VJY29uIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwcm9kdWN0Lm5hbWV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3QuYnJhbmQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj57cHJvZHVjdC5icmFuZH08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTEwMCByb3VuZGVkLWZ1bGwgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0LmNhdGVnb3J5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtICR7c3RvY2tTdGF0dXMuY29sb3J9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5zdG9ja30ge3Byb2R1Y3QudW5pdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3N0b2NrU3RhdHVzLnN0YXR1cyAhPT0gJ2dvb2QnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1vcmFuZ2UtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRDdXJyZW5jeShwcm9kdWN0LnByaWNlX2R6ZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB5LTMgcHgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXhwaXJ5U3RhdHVzID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17ZXhwaXJ5U3RhdHVzLmNvbG9yfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtleHBpcnlTdGF0dXMudGV4dH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPk5vIGV4cGlyeTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHktMyBweC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZFByb2R1Y3QocHJvZHVjdCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRQcm9kdWN0KHByb2R1Y3QpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dBZGRQcm9kdWN0KHRydWUpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFZGl0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFByb2R1Y3QocHJvZHVjdClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd1N0b2NrQWRqdXN0bWVudCh0cnVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTUwMCBob3Zlcjp0ZXh0LW9yYW5nZS03MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJBZGp1c3QgU3RvY2tcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlUHJvZHVjdChwcm9kdWN0LmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCBob3Zlcjp0ZXh0LXJlZC03MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAge2ZpbHRlcmVkUHJvZHVjdHMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTggdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgTm8gcHJvZHVjdHMgZm91bmQgbWF0Y2hpbmcgeW91ciBjcml0ZXJpYVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2RhbHMgKi99XG4gICAgICB7c2hvd0FkZFByb2R1Y3QgJiYgKFxuICAgICAgICA8QWRkUHJvZHVjdE1vZGFsXG4gICAgICAgICAgcHJvZHVjdD17c2VsZWN0ZWRQcm9kdWN0fVxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHtcbiAgICAgICAgICAgIHNldFNob3dBZGRQcm9kdWN0KGZhbHNlKVxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRQcm9kdWN0KG51bGwpXG4gICAgICAgICAgfX1cbiAgICAgICAgICBvblNhdmU9e2hhbmRsZVByb2R1Y3RVcGRhdGV9XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICB7c2hvd1N1cHBsaWVycyAmJiAoXG4gICAgICAgIDxTdXBwbGllcnNNb2RhbFxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dTdXBwbGllcnMoZmFsc2UpfVxuICAgICAgICAgIG9uVXBkYXRlPXtsb2FkRGF0YX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHtzaG93UHVyY2hhc2UgJiYgKFxuICAgICAgICA8UHVyY2hhc2VNb2RhbFxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dQdXJjaGFzZShmYWxzZSl9XG4gICAgICAgICAgb25TYXZlPXtoYW5kbGVQcm9kdWN0VXBkYXRlfVxuICAgICAgICAgIHN1cHBsaWVycz17c3VwcGxpZXJzfVxuICAgICAgICAgIHByb2R1Y3RzPXtwcm9kdWN0c31cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHtzaG93U3RvY2tIaXN0b3J5ICYmIChcbiAgICAgICAgPFN0b2NrTW92ZW1lbnRIaXN0b3J5XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd1N0b2NrSGlzdG9yeShmYWxzZSl9XG4gICAgICAgICAgbW92ZW1lbnRzPXtzdG9ja01vdmVtZW50c31cbiAgICAgICAgICBwcm9kdWN0cz17cHJvZHVjdHN9XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICB7c2hvd1N0b2NrQWRqdXN0bWVudCAmJiBzZWxlY3RlZFByb2R1Y3QgJiYgKFxuICAgICAgICA8U3RvY2tBZGp1c3RtZW50TW9kYWxcbiAgICAgICAgICBwcm9kdWN0PXtzZWxlY3RlZFByb2R1Y3R9XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4ge1xuICAgICAgICAgICAgc2V0U2hvd1N0b2NrQWRqdXN0bWVudChmYWxzZSlcbiAgICAgICAgICAgIHNldFNlbGVjdGVkUHJvZHVjdChudWxsKVxuICAgICAgICAgIH19XG4gICAgICAgICAgb25TYXZlPXtoYW5kbGVQcm9kdWN0VXBkYXRlfVxuICAgICAgICAvPlxuICAgICAgKX1cbiAgICA8L01haW5MYXlvdXQ+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIk1haW5MYXlvdXQiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwidXNlTGFuZ3VhZ2UiLCJ1c2VUb2FzdCIsIkludmVudG9yeVN0b3JhZ2UiLCJmb3JtYXRDdXJyZW5jeSIsIlBhY2thZ2UiLCJQbHVzIiwiQWxlcnRUcmlhbmdsZSIsIlRyZW5kaW5nVXAiLCJUcmVuZGluZ0Rvd24iLCJVc2VycyIsIlNob3BwaW5nQ2FydCIsIkNhbGVuZGFyIiwiU2VhcmNoIiwiRG93bmxvYWQiLCJFeWUiLCJFZGl0IiwiVHJhc2gyIiwiSW1hZ2UiLCJJbWFnZUljb24iLCJGaWxlVGV4dCIsIkFkZFByb2R1Y3RNb2RhbCIsIlN1cHBsaWVyc01vZGFsIiwiUHVyY2hhc2VNb2RhbCIsIlN0b2NrTW92ZW1lbnRIaXN0b3J5IiwiU3RvY2tBZGp1c3RtZW50TW9kYWwiLCJJbnZlbnRvcnlQYWdlIiwidCIsInRvYXN0IiwicHJvZHVjdHMiLCJzZXRQcm9kdWN0cyIsInN1cHBsaWVycyIsInNldFN1cHBsaWVycyIsInN0b2NrTW92ZW1lbnRzIiwic2V0U3RvY2tNb3ZlbWVudHMiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5Iiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJzaG93TG93U3RvY2siLCJzZXRTaG93TG93U3RvY2siLCJzaG93RXhwaXJpbmciLCJzZXRTaG93RXhwaXJpbmciLCJzaG93QWRkUHJvZHVjdCIsInNldFNob3dBZGRQcm9kdWN0Iiwic2hvd1N1cHBsaWVycyIsInNldFNob3dTdXBwbGllcnMiLCJzaG93UHVyY2hhc2UiLCJzZXRTaG93UHVyY2hhc2UiLCJzaG93U3RvY2tIaXN0b3J5Iiwic2V0U2hvd1N0b2NrSGlzdG9yeSIsInNob3dTdG9ja0FkanVzdG1lbnQiLCJzZXRTaG93U3RvY2tBZGp1c3RtZW50Iiwic2hvd1JlcG9ydHMiLCJzZXRTaG93UmVwb3J0cyIsInNlbGVjdGVkUHJvZHVjdCIsInNldFNlbGVjdGVkUHJvZHVjdCIsImxvYWREYXRhIiwiaW5pdGlhbGl6ZURlZmF1bHREYXRhIiwicHJvZHVjdHNEYXRhIiwiZ2V0RnJvbVN0b3JhZ2UiLCJzdXBwbGllcnNEYXRhIiwiZ2V0U3VwcGxpZXJzIiwibW92ZW1lbnRzRGF0YSIsImdldFN0b2NrTW92ZW1lbnRzIiwiZmlsdGVyIiwicyIsImFjdGl2ZSIsImVycm9yIiwiY29uc29sZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiZmlsdGVyZWRQcm9kdWN0cyIsInByb2R1Y3QiLCJtYXRjaGVzU2VhcmNoIiwibmFtZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJjYXRlZ29yeSIsImJyYW5kIiwibWF0Y2hlc0NhdGVnb3J5IiwibWF0Y2hlc0xvd1N0b2NrIiwic3RvY2siLCJtaW5fc3RvY2siLCJtYXRjaGVzRXhwaXJpbmciLCJleHBpcnlfZGF0ZSIsIkRhdGUiLCJub3ciLCJzdGF0cyIsInRvdGFsUHJvZHVjdHMiLCJsZW5ndGgiLCJ0b3RhbFZhbHVlIiwicmVkdWNlIiwic3VtIiwicCIsInByaWNlX2R6ZCIsImxvd1N0b2NrQ291bnQiLCJleHBpcmluZ0NvdW50IiwiYWN0aXZlU3VwcGxpZXJzIiwiY2F0ZWdvcmllcyIsIlNldCIsIm1hcCIsImhhbmRsZVByb2R1Y3RVcGRhdGUiLCJoYW5kbGVEZWxldGVQcm9kdWN0IiwicHJvZHVjdElkIiwiY29uZmlybSIsInVwZGF0ZWRQcm9kdWN0cyIsImlkIiwic2F2ZVRvU3RvcmFnZSIsImdldFN0b2NrU3RhdHVzIiwic3RhdHVzIiwiY29sb3IiLCJiZyIsImdldEV4cGlyeVN0YXR1cyIsImV4cGlyeURhdGUiLCJ0b2RheSIsImV4cGlyeSIsImRheXNVbnRpbEV4cGlyeSIsIk1hdGgiLCJjZWlsIiwiZ2V0VGltZSIsInRleHQiLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInNlbGVjdCIsIm9wdGlvbiIsInNpemUiLCJzcGFuIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInN0b2NrU3RhdHVzIiwiZXhwaXJ5U3RhdHVzIiwidGQiLCJpbWFnZV91cmwiLCJpbWciLCJzcmMiLCJhbHQiLCJ1bml0Iiwib25DbG9zZSIsIm9uU2F2ZSIsIm9uVXBkYXRlIiwibW92ZW1lbnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ })

});