"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/lib/inventory-storage.ts":
/*!**************************************!*\
  !*** ./src/lib/inventory-storage.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InventoryStorage: function() { return /* binding */ InventoryStorage; }\n/* harmony export */ });\n// localStorage-based inventory management system\n// localStorage keys\nconst STORAGE_KEYS = {\n    SUPPLIERS: \"gym_suppliers\",\n    PURCHASES: \"gym_purchases\",\n    PURCHASE_ITEMS: \"gym_purchase_items\",\n    SUPPLIER_PAYMENTS: \"gym_supplier_payments\",\n    STOCK_MOVEMENTS: \"gym_stock_movements\",\n    CATEGORIES: \"gym_categories\",\n    PRODUCTS: \"gym_products\"\n};\n// Utility functions for localStorage operations\nclass InventoryStorage {\n    // Generic storage operations\n    static getFromStorage(key) {\n        try {\n            const data = localStorage.getItem(key);\n            return data ? JSON.parse(data) : [];\n        } catch (error) {\n            console.error(\"Error reading \".concat(key, \" from localStorage:\"), error);\n            return [];\n        }\n    }\n    static saveToStorage(key, data) {\n        try {\n            localStorage.setItem(key, JSON.stringify(data));\n        } catch (error) {\n            console.error(\"Error saving \".concat(key, \" to localStorage:\"), error);\n        }\n    }\n    static generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    // Supplier operations\n    static getSuppliers() {\n        return this.getFromStorage(STORAGE_KEYS.SUPPLIERS);\n    }\n    static saveSuppliers(suppliers) {\n        this.saveToStorage(STORAGE_KEYS.SUPPLIERS, suppliers);\n    }\n    static addSupplier(supplier) {\n        const suppliers = this.getSuppliers();\n        const newSupplier = {\n            ...supplier,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        suppliers.push(newSupplier);\n        this.saveSuppliers(suppliers);\n        return newSupplier;\n    }\n    static updateSupplier(id, updates) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === id);\n        if (index === -1) return null;\n        suppliers[index] = {\n            ...suppliers[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        this.saveSuppliers(suppliers);\n        return suppliers[index];\n    }\n    static deleteSupplier(id) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === id);\n        if (index === -1) return false;\n        suppliers[index].active = false;\n        this.saveSuppliers(suppliers);\n        return true;\n    }\n    static getSupplierById(id) {\n        const suppliers = this.getSuppliers();\n        return suppliers.find((s)=>s.id === id) || null;\n    }\n    // Purchase operations\n    static getPurchases() {\n        return this.getFromStorage(STORAGE_KEYS.PURCHASES);\n    }\n    static savePurchases(purchases) {\n        this.saveToStorage(STORAGE_KEYS.PURCHASES, purchases);\n    }\n    static addPurchase(purchase) {\n        const purchases = this.getPurchases();\n        const newPurchase = {\n            ...purchase,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        purchases.push(newPurchase);\n        this.savePurchases(purchases);\n        // Update supplier balance if credit purchase\n        if (purchase.payment_type === \"credit\") {\n            this.updateSupplierBalance(purchase.supplier_id, purchase.total_amount);\n        }\n        return newPurchase;\n    }\n    static updatePurchase(id, updates) {\n        const purchases = this.getPurchases();\n        const index = purchases.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        purchases[index] = {\n            ...purchases[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        this.savePurchases(purchases);\n        return purchases[index];\n    }\n    // Purchase Items operations\n    static getPurchaseItems() {\n        return this.getFromStorage(STORAGE_KEYS.PURCHASE_ITEMS);\n    }\n    static savePurchaseItems(items) {\n        this.saveToStorage(STORAGE_KEYS.PURCHASE_ITEMS, items);\n    }\n    static addPurchaseItem(item) {\n        const items = this.getPurchaseItems();\n        const newItem = {\n            ...item,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        items.push(newItem);\n        this.savePurchaseItems(items);\n        // Update product stock\n        this.updateProductStock(item.product_id, item.quantity, \"purchase\", newItem.id);\n        return newItem;\n    }\n    static getPurchaseItemsByPurchaseId(purchaseId) {\n        const items = this.getPurchaseItems();\n        return items.filter((item)=>item.purchase_id === purchaseId);\n    }\n    // Supplier Payment operations\n    static getSupplierPayments() {\n        return this.getFromStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS);\n    }\n    static saveSupplierPayments(payments) {\n        this.saveToStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS, payments);\n    }\n    static addSupplierPayment(payment) {\n        const payments = this.getSupplierPayments();\n        const newPayment = {\n            ...payment,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        payments.push(newPayment);\n        this.saveSupplierPayments(payments);\n        // Update supplier balance\n        this.updateSupplierBalance(payment.supplier_id, -payment.amount);\n        // Update purchase payment status if purchase_id provided\n        if (payment.purchase_id) {\n            this.updatePurchasePaymentStatus(payment.purchase_id, payment.amount);\n        }\n        return newPayment;\n    }\n    static getSupplierPaymentsBySupplier(supplierId) {\n        const payments = this.getSupplierPayments();\n        return payments.filter((payment)=>payment.supplier_id === supplierId);\n    }\n    // Helper methods\n    static updateSupplierBalance(supplierId, amount) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === supplierId);\n        if (index !== -1) {\n            suppliers[index].current_balance += amount;\n            suppliers[index].updated_at = new Date().toISOString();\n            this.saveSuppliers(suppliers);\n        }\n    }\n    static updatePurchasePaymentStatus(purchaseId, paymentAmount) {\n        const purchases = this.getPurchases();\n        const index = purchases.findIndex((p)=>p.id === purchaseId);\n        if (index !== -1) {\n            const purchase = purchases[index];\n            purchase.paid_amount += paymentAmount;\n            purchase.remaining_balance = purchase.total_amount - purchase.paid_amount;\n            if (purchase.remaining_balance <= 0) {\n                purchase.payment_status = \"paid\";\n            } else if (purchase.paid_amount > 0) {\n                purchase.payment_status = \"partial\";\n            }\n            purchase.updated_at = new Date().toISOString();\n            this.savePurchases(purchases);\n        }\n    }\n    static updateProductStock(productId, quantity, movementType, referenceId) {\n        // Get current products (assuming they're stored in localStorage)\n        const products = this.getFromStorage(STORAGE_KEYS.PRODUCTS);\n        const productIndex = products.findIndex((p)=>p.id === productId);\n        if (productIndex !== -1) {\n            const product = products[productIndex];\n            const previousStock = product.stock;\n            const newStock = previousStock + quantity;\n            // Update product stock\n            product.stock = newStock;\n            product.updated_at = new Date().toISOString();\n            this.saveToStorage(STORAGE_KEYS.PRODUCTS, products);\n            // Create stock movement record\n            this.addStockMovement({\n                product_id: productId,\n                product_name: product.name,\n                movement_type: movementType,\n                quantity_change: quantity,\n                previous_stock: previousStock,\n                new_stock: newStock,\n                reference_id: referenceId,\n                reference_type: movementType,\n                notes: \"Stock \".concat(movementType, \" - \").concat(quantity, \" units\")\n            });\n        }\n    }\n    // Stock Movement operations\n    static getStockMovements() {\n        return this.getFromStorage(STORAGE_KEYS.STOCK_MOVEMENTS);\n    }\n    static saveStockMovements(movements) {\n        this.saveToStorage(STORAGE_KEYS.STOCK_MOVEMENTS, movements);\n    }\n    static addStockMovement(movement) {\n        const movements = this.getStockMovements();\n        const newMovement = {\n            ...movement,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        movements.push(newMovement);\n        this.saveStockMovements(movements);\n        return newMovement;\n    }\n    static getStockMovementsByProduct(productId) {\n        const movements = this.getStockMovements();\n        return movements.filter((movement)=>movement.product_id === productId);\n    }\n    // Category operations\n    static getCategories() {\n        return this.getFromStorage(STORAGE_KEYS.CATEGORIES);\n    }\n    static saveCategories(categories) {\n        this.saveToStorage(STORAGE_KEYS.CATEGORIES, categories);\n    }\n    static addCategory(category) {\n        const categories = this.getCategories();\n        const newCategory = {\n            ...category,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        categories.push(newCategory);\n        this.saveCategories(categories);\n        return newCategory;\n    }\n    // Initialize default data\n    static initializeDefaultData() {\n        // Initialize default categories if none exist\n        const categories = this.getCategories();\n        if (categories.length === 0) {\n            const defaultCategories = [\n                {\n                    name: \"Supplements\",\n                    description: \"Protein powders, vitamins, and nutritional supplements\"\n                },\n                {\n                    name: \"Beverages\",\n                    description: \"Energy drinks, water, sports drinks\"\n                },\n                {\n                    name: \"Equipment\",\n                    description: \"Gym equipment, accessories, and gear\"\n                },\n                {\n                    name: \"Apparel\",\n                    description: \"Gym clothing, shoes, and accessories\"\n                },\n                {\n                    name: \"Snacks\",\n                    description: \"Protein bars, healthy snacks\"\n                },\n                {\n                    name: \"Personal Care\",\n                    description: \"Towels, toiletries, hygiene products\"\n                },\n                {\n                    name: \"Accessories\",\n                    description: \"Gloves, belts, straps, and other accessories\"\n                },\n                {\n                    name: \"Recovery\",\n                    description: \"Recovery tools, massage equipment\"\n                }\n            ];\n            defaultCategories.forEach((cat)=>{\n                this.addCategory({\n                    ...cat,\n                    active: true\n                });\n            });\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvaW52ZW50b3J5LXN0b3JhZ2UudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlEQUFpRDtBQXFHakQsb0JBQW9CO0FBQ3BCLE1BQU1BLGVBQWU7SUFDbkJDLFdBQVc7SUFDWEMsV0FBVztJQUNYQyxnQkFBZ0I7SUFDaEJDLG1CQUFtQjtJQUNuQkMsaUJBQWlCO0lBQ2pCQyxZQUFZO0lBQ1pDLFVBQVU7QUFDWjtBQUVBLGdEQUFnRDtBQUN6QyxNQUFNQztJQUNYLDZCQUE2QjtJQUM3QixPQUFPQyxlQUFrQkMsR0FBVyxFQUFPO1FBQ3pDLElBQUk7WUFDRixNQUFNQyxPQUFPQyxhQUFhQyxPQUFPLENBQUNIO1lBQ2xDLE9BQU9DLE9BQU9HLEtBQUtDLEtBQUssQ0FBQ0osUUFBUSxFQUFFO1FBQ3JDLEVBQUUsT0FBT0ssT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUJBQXFCLE9BQUpOLEtBQUksd0JBQXNCTTtZQUN6RCxPQUFPLEVBQUU7UUFDWDtJQUNGO0lBRUEsT0FBT0UsY0FBaUJSLEdBQVcsRUFBRUMsSUFBUyxFQUFRO1FBQ3BELElBQUk7WUFDRkMsYUFBYU8sT0FBTyxDQUFDVCxLQUFLSSxLQUFLTSxTQUFTLENBQUNUO1FBQzNDLEVBQUUsT0FBT0ssT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0JBQW9CLE9BQUpOLEtBQUksc0JBQW9CTTtRQUN4RDtJQUNGO0lBRUEsT0FBZUssYUFBcUI7UUFDbEMsT0FBT0MsS0FBS0MsR0FBRyxHQUFHQyxRQUFRLENBQUMsTUFBTUMsS0FBS0MsTUFBTSxHQUFHRixRQUFRLENBQUMsSUFBSUcsTUFBTSxDQUFDO0lBQ3JFO0lBRUEsc0JBQXNCO0lBQ3RCLE9BQU9DLGVBQTJCO1FBQ2hDLE9BQU8sSUFBSSxDQUFDbkIsY0FBYyxDQUFXVCxhQUFhQyxTQUFTO0lBQzdEO0lBRUEsT0FBTzRCLGNBQWNDLFNBQXFCLEVBQVE7UUFDaEQsSUFBSSxDQUFDWixhQUFhLENBQUNsQixhQUFhQyxTQUFTLEVBQUU2QjtJQUM3QztJQUVBLE9BQU9DLFlBQVlDLFFBQTRELEVBQVk7UUFDekYsTUFBTUYsWUFBWSxJQUFJLENBQUNGLFlBQVk7UUFDbkMsTUFBTUssY0FBd0I7WUFDNUIsR0FBR0QsUUFBUTtZQUNYRSxJQUFJLElBQUksQ0FBQ2IsVUFBVTtZQUNuQmMsWUFBWSxJQUFJYixPQUFPYyxXQUFXO1lBQ2xDQyxZQUFZLElBQUlmLE9BQU9jLFdBQVc7UUFDcEM7UUFDQU4sVUFBVVEsSUFBSSxDQUFDTDtRQUNmLElBQUksQ0FBQ0osYUFBYSxDQUFDQztRQUNuQixPQUFPRztJQUNUO0lBRUEsT0FBT00sZUFBZUwsRUFBVSxFQUFFTSxPQUEwQixFQUFtQjtRQUM3RSxNQUFNVixZQUFZLElBQUksQ0FBQ0YsWUFBWTtRQUNuQyxNQUFNYSxRQUFRWCxVQUFVWSxTQUFTLENBQUNDLENBQUFBLElBQUtBLEVBQUVULEVBQUUsS0FBS0E7UUFDaEQsSUFBSU8sVUFBVSxDQUFDLEdBQUcsT0FBTztRQUV6QlgsU0FBUyxDQUFDVyxNQUFNLEdBQUc7WUFDakIsR0FBR1gsU0FBUyxDQUFDVyxNQUFNO1lBQ25CLEdBQUdELE9BQU87WUFDVkgsWUFBWSxJQUFJZixPQUFPYyxXQUFXO1FBQ3BDO1FBQ0EsSUFBSSxDQUFDUCxhQUFhLENBQUNDO1FBQ25CLE9BQU9BLFNBQVMsQ0FBQ1csTUFBTTtJQUN6QjtJQUVBLE9BQU9HLGVBQWVWLEVBQVUsRUFBVztRQUN6QyxNQUFNSixZQUFZLElBQUksQ0FBQ0YsWUFBWTtRQUNuQyxNQUFNYSxRQUFRWCxVQUFVWSxTQUFTLENBQUNDLENBQUFBLElBQUtBLEVBQUVULEVBQUUsS0FBS0E7UUFDaEQsSUFBSU8sVUFBVSxDQUFDLEdBQUcsT0FBTztRQUV6QlgsU0FBUyxDQUFDVyxNQUFNLENBQUNJLE1BQU0sR0FBRztRQUMxQixJQUFJLENBQUNoQixhQUFhLENBQUNDO1FBQ25CLE9BQU87SUFDVDtJQUVBLE9BQU9nQixnQkFBZ0JaLEVBQVUsRUFBbUI7UUFDbEQsTUFBTUosWUFBWSxJQUFJLENBQUNGLFlBQVk7UUFDbkMsT0FBT0UsVUFBVWlCLElBQUksQ0FBQ0osQ0FBQUEsSUFBS0EsRUFBRVQsRUFBRSxLQUFLQSxPQUFPO0lBQzdDO0lBRUEsc0JBQXNCO0lBQ3RCLE9BQU9jLGVBQTJCO1FBQ2hDLE9BQU8sSUFBSSxDQUFDdkMsY0FBYyxDQUFXVCxhQUFhRSxTQUFTO0lBQzdEO0lBRUEsT0FBTytDLGNBQWNDLFNBQXFCLEVBQVE7UUFDaEQsSUFBSSxDQUFDaEMsYUFBYSxDQUFDbEIsYUFBYUUsU0FBUyxFQUFFZ0Q7SUFDN0M7SUFFQSxPQUFPQyxZQUFZQyxRQUE0RCxFQUFZO1FBQ3pGLE1BQU1GLFlBQVksSUFBSSxDQUFDRixZQUFZO1FBQ25DLE1BQU1LLGNBQXdCO1lBQzVCLEdBQUdELFFBQVE7WUFDWGxCLElBQUksSUFBSSxDQUFDYixVQUFVO1lBQ25CYyxZQUFZLElBQUliLE9BQU9jLFdBQVc7WUFDbENDLFlBQVksSUFBSWYsT0FBT2MsV0FBVztRQUNwQztRQUNBYyxVQUFVWixJQUFJLENBQUNlO1FBQ2YsSUFBSSxDQUFDSixhQUFhLENBQUNDO1FBRW5CLDZDQUE2QztRQUM3QyxJQUFJRSxTQUFTRSxZQUFZLEtBQUssVUFBVTtZQUN0QyxJQUFJLENBQUNDLHFCQUFxQixDQUFDSCxTQUFTSSxXQUFXLEVBQUVKLFNBQVNLLFlBQVk7UUFDeEU7UUFFQSxPQUFPSjtJQUNUO0lBRUEsT0FBT0ssZUFBZXhCLEVBQVUsRUFBRU0sT0FBMEIsRUFBbUI7UUFDN0UsTUFBTVUsWUFBWSxJQUFJLENBQUNGLFlBQVk7UUFDbkMsTUFBTVAsUUFBUVMsVUFBVVIsU0FBUyxDQUFDaUIsQ0FBQUEsSUFBS0EsRUFBRXpCLEVBQUUsS0FBS0E7UUFDaEQsSUFBSU8sVUFBVSxDQUFDLEdBQUcsT0FBTztRQUV6QlMsU0FBUyxDQUFDVCxNQUFNLEdBQUc7WUFDakIsR0FBR1MsU0FBUyxDQUFDVCxNQUFNO1lBQ25CLEdBQUdELE9BQU87WUFDVkgsWUFBWSxJQUFJZixPQUFPYyxXQUFXO1FBQ3BDO1FBQ0EsSUFBSSxDQUFDYSxhQUFhLENBQUNDO1FBQ25CLE9BQU9BLFNBQVMsQ0FBQ1QsTUFBTTtJQUN6QjtJQUVBLDRCQUE0QjtJQUM1QixPQUFPbUIsbUJBQW1DO1FBQ3hDLE9BQU8sSUFBSSxDQUFDbkQsY0FBYyxDQUFlVCxhQUFhRyxjQUFjO0lBQ3RFO0lBRUEsT0FBTzBELGtCQUFrQkMsS0FBcUIsRUFBUTtRQUNwRCxJQUFJLENBQUM1QyxhQUFhLENBQUNsQixhQUFhRyxjQUFjLEVBQUUyRDtJQUNsRDtJQUVBLE9BQU9DLGdCQUFnQkMsSUFBNkMsRUFBZ0I7UUFDbEYsTUFBTUYsUUFBUSxJQUFJLENBQUNGLGdCQUFnQjtRQUNuQyxNQUFNSyxVQUF3QjtZQUM1QixHQUFHRCxJQUFJO1lBQ1A5QixJQUFJLElBQUksQ0FBQ2IsVUFBVTtZQUNuQmMsWUFBWSxJQUFJYixPQUFPYyxXQUFXO1FBQ3BDO1FBQ0EwQixNQUFNeEIsSUFBSSxDQUFDMkI7UUFDWCxJQUFJLENBQUNKLGlCQUFpQixDQUFDQztRQUV2Qix1QkFBdUI7UUFDdkIsSUFBSSxDQUFDSSxrQkFBa0IsQ0FBQ0YsS0FBS0csVUFBVSxFQUFFSCxLQUFLSSxRQUFRLEVBQUUsWUFBWUgsUUFBUS9CLEVBQUU7UUFFOUUsT0FBTytCO0lBQ1Q7SUFFQSxPQUFPSSw2QkFBNkJDLFVBQWtCLEVBQWtCO1FBQ3RFLE1BQU1SLFFBQVEsSUFBSSxDQUFDRixnQkFBZ0I7UUFDbkMsT0FBT0UsTUFBTVMsTUFBTSxDQUFDUCxDQUFBQSxPQUFRQSxLQUFLUSxXQUFXLEtBQUtGO0lBQ25EO0lBRUEsOEJBQThCO0lBQzlCLE9BQU9HLHNCQUF5QztRQUM5QyxPQUFPLElBQUksQ0FBQ2hFLGNBQWMsQ0FBa0JULGFBQWFJLGlCQUFpQjtJQUM1RTtJQUVBLE9BQU9zRSxxQkFBcUJDLFFBQTJCLEVBQVE7UUFDN0QsSUFBSSxDQUFDekQsYUFBYSxDQUFDbEIsYUFBYUksaUJBQWlCLEVBQUV1RTtJQUNyRDtJQUVBLE9BQU9DLG1CQUFtQkMsT0FBbUQsRUFBbUI7UUFDOUYsTUFBTUYsV0FBVyxJQUFJLENBQUNGLG1CQUFtQjtRQUN6QyxNQUFNSyxhQUE4QjtZQUNsQyxHQUFHRCxPQUFPO1lBQ1YzQyxJQUFJLElBQUksQ0FBQ2IsVUFBVTtZQUNuQmMsWUFBWSxJQUFJYixPQUFPYyxXQUFXO1FBQ3BDO1FBQ0F1QyxTQUFTckMsSUFBSSxDQUFDd0M7UUFDZCxJQUFJLENBQUNKLG9CQUFvQixDQUFDQztRQUUxQiwwQkFBMEI7UUFDMUIsSUFBSSxDQUFDcEIscUJBQXFCLENBQUNzQixRQUFRckIsV0FBVyxFQUFFLENBQUNxQixRQUFRRSxNQUFNO1FBRS9ELHlEQUF5RDtRQUN6RCxJQUFJRixRQUFRTCxXQUFXLEVBQUU7WUFDdkIsSUFBSSxDQUFDUSwyQkFBMkIsQ0FBQ0gsUUFBUUwsV0FBVyxFQUFFSyxRQUFRRSxNQUFNO1FBQ3RFO1FBRUEsT0FBT0Q7SUFDVDtJQUVBLE9BQU9HLDhCQUE4QkMsVUFBa0IsRUFBcUI7UUFDMUUsTUFBTVAsV0FBVyxJQUFJLENBQUNGLG1CQUFtQjtRQUN6QyxPQUFPRSxTQUFTSixNQUFNLENBQUNNLENBQUFBLFVBQVdBLFFBQVFyQixXQUFXLEtBQUswQjtJQUM1RDtJQUVBLGlCQUFpQjtJQUNqQixPQUFlM0Isc0JBQXNCMkIsVUFBa0IsRUFBRUgsTUFBYyxFQUFRO1FBQzdFLE1BQU1qRCxZQUFZLElBQUksQ0FBQ0YsWUFBWTtRQUNuQyxNQUFNYSxRQUFRWCxVQUFVWSxTQUFTLENBQUNDLENBQUFBLElBQUtBLEVBQUVULEVBQUUsS0FBS2dEO1FBQ2hELElBQUl6QyxVQUFVLENBQUMsR0FBRztZQUNoQlgsU0FBUyxDQUFDVyxNQUFNLENBQUMwQyxlQUFlLElBQUlKO1lBQ3BDakQsU0FBUyxDQUFDVyxNQUFNLENBQUNKLFVBQVUsR0FBRyxJQUFJZixPQUFPYyxXQUFXO1lBQ3BELElBQUksQ0FBQ1AsYUFBYSxDQUFDQztRQUNyQjtJQUNGO0lBRUEsT0FBZWtELDRCQUE0QlYsVUFBa0IsRUFBRWMsYUFBcUIsRUFBUTtRQUMxRixNQUFNbEMsWUFBWSxJQUFJLENBQUNGLFlBQVk7UUFDbkMsTUFBTVAsUUFBUVMsVUFBVVIsU0FBUyxDQUFDaUIsQ0FBQUEsSUFBS0EsRUFBRXpCLEVBQUUsS0FBS29DO1FBQ2hELElBQUk3QixVQUFVLENBQUMsR0FBRztZQUNoQixNQUFNVyxXQUFXRixTQUFTLENBQUNULE1BQU07WUFDakNXLFNBQVNpQyxXQUFXLElBQUlEO1lBQ3hCaEMsU0FBU2tDLGlCQUFpQixHQUFHbEMsU0FBU0ssWUFBWSxHQUFHTCxTQUFTaUMsV0FBVztZQUV6RSxJQUFJakMsU0FBU2tDLGlCQUFpQixJQUFJLEdBQUc7Z0JBQ25DbEMsU0FBU21DLGNBQWMsR0FBRztZQUM1QixPQUFPLElBQUluQyxTQUFTaUMsV0FBVyxHQUFHLEdBQUc7Z0JBQ25DakMsU0FBU21DLGNBQWMsR0FBRztZQUM1QjtZQUVBbkMsU0FBU2YsVUFBVSxHQUFHLElBQUlmLE9BQU9jLFdBQVc7WUFDNUMsSUFBSSxDQUFDYSxhQUFhLENBQUNDO1FBQ3JCO0lBQ0Y7SUFFQSxPQUFlZ0IsbUJBQW1Cc0IsU0FBaUIsRUFBRXBCLFFBQWdCLEVBQUVxQixZQUE0QyxFQUFFQyxXQUFtQixFQUFRO1FBQzlJLGlFQUFpRTtRQUNqRSxNQUFNQyxXQUFXLElBQUksQ0FBQ2xGLGNBQWMsQ0FBa0JULGFBQWFPLFFBQVE7UUFDM0UsTUFBTXFGLGVBQWVELFNBQVNqRCxTQUFTLENBQUNpQixDQUFBQSxJQUFLQSxFQUFFekIsRUFBRSxLQUFLc0Q7UUFFdEQsSUFBSUksaUJBQWlCLENBQUMsR0FBRztZQUN2QixNQUFNQyxVQUFVRixRQUFRLENBQUNDLGFBQWE7WUFDdEMsTUFBTUUsZ0JBQWdCRCxRQUFRRSxLQUFLO1lBQ25DLE1BQU1DLFdBQVdGLGdCQUFnQjFCO1lBRWpDLHVCQUF1QjtZQUN2QnlCLFFBQVFFLEtBQUssR0FBR0M7WUFDaEJILFFBQVF4RCxVQUFVLEdBQUcsSUFBSWYsT0FBT2MsV0FBVztZQUMzQyxJQUFJLENBQUNsQixhQUFhLENBQUNsQixhQUFhTyxRQUFRLEVBQUVvRjtZQUUxQywrQkFBK0I7WUFDL0IsSUFBSSxDQUFDTSxnQkFBZ0IsQ0FBQztnQkFDcEI5QixZQUFZcUI7Z0JBQ1pVLGNBQWNMLFFBQVFNLElBQUk7Z0JBQzFCQyxlQUFlWDtnQkFDZlksaUJBQWlCakM7Z0JBQ2pCa0MsZ0JBQWdCUjtnQkFDaEJTLFdBQVdQO2dCQUNYUSxjQUFjZDtnQkFDZGUsZ0JBQWdCaEI7Z0JBQ2hCaUIsT0FBTyxTQUEyQnRDLE9BQWxCcUIsY0FBYSxPQUFjLE9BQVRyQixVQUFTO1lBQzdDO1FBQ0Y7SUFDRjtJQUVBLDRCQUE0QjtJQUM1QixPQUFPdUMsb0JBQXFDO1FBQzFDLE9BQU8sSUFBSSxDQUFDbEcsY0FBYyxDQUFnQlQsYUFBYUssZUFBZTtJQUN4RTtJQUVBLE9BQU91RyxtQkFBbUJDLFNBQTBCLEVBQVE7UUFDMUQsSUFBSSxDQUFDM0YsYUFBYSxDQUFDbEIsYUFBYUssZUFBZSxFQUFFd0c7SUFDbkQ7SUFFQSxPQUFPWixpQkFBaUJhLFFBQWtELEVBQWlCO1FBQ3pGLE1BQU1ELFlBQVksSUFBSSxDQUFDRixpQkFBaUI7UUFDeEMsTUFBTUksY0FBNkI7WUFDakMsR0FBR0QsUUFBUTtZQUNYNUUsSUFBSSxJQUFJLENBQUNiLFVBQVU7WUFDbkJjLFlBQVksSUFBSWIsT0FBT2MsV0FBVztRQUNwQztRQUNBeUUsVUFBVXZFLElBQUksQ0FBQ3lFO1FBQ2YsSUFBSSxDQUFDSCxrQkFBa0IsQ0FBQ0M7UUFDeEIsT0FBT0U7SUFDVDtJQUVBLE9BQU9DLDJCQUEyQnhCLFNBQWlCLEVBQW1CO1FBQ3BFLE1BQU1xQixZQUFZLElBQUksQ0FBQ0YsaUJBQWlCO1FBQ3hDLE9BQU9FLFVBQVV0QyxNQUFNLENBQUN1QyxDQUFBQSxXQUFZQSxTQUFTM0MsVUFBVSxLQUFLcUI7SUFDOUQ7SUFFQSxzQkFBc0I7SUFDdEIsT0FBT3lCLGdCQUE0QjtRQUNqQyxPQUFPLElBQUksQ0FBQ3hHLGNBQWMsQ0FBV1QsYUFBYU0sVUFBVTtJQUM5RDtJQUVBLE9BQU80RyxlQUFlQyxVQUFzQixFQUFRO1FBQ2xELElBQUksQ0FBQ2pHLGFBQWEsQ0FBQ2xCLGFBQWFNLFVBQVUsRUFBRTZHO0lBQzlDO0lBRUEsT0FBT0MsWUFBWUMsUUFBNEQsRUFBWTtRQUN6RixNQUFNRixhQUFhLElBQUksQ0FBQ0YsYUFBYTtRQUNyQyxNQUFNSyxjQUF3QjtZQUM1QixHQUFHRCxRQUFRO1lBQ1huRixJQUFJLElBQUksQ0FBQ2IsVUFBVTtZQUNuQmMsWUFBWSxJQUFJYixPQUFPYyxXQUFXO1lBQ2xDQyxZQUFZLElBQUlmLE9BQU9jLFdBQVc7UUFDcEM7UUFDQStFLFdBQVc3RSxJQUFJLENBQUNnRjtRQUNoQixJQUFJLENBQUNKLGNBQWMsQ0FBQ0M7UUFDcEIsT0FBT0c7SUFDVDtJQUVBLDBCQUEwQjtJQUMxQixPQUFPQyx3QkFBOEI7UUFDbkMsOENBQThDO1FBQzlDLE1BQU1KLGFBQWEsSUFBSSxDQUFDRixhQUFhO1FBQ3JDLElBQUlFLFdBQVdLLE1BQU0sS0FBSyxHQUFHO1lBQzNCLE1BQU1DLG9CQUFvQjtnQkFDeEI7b0JBQUV0QixNQUFNO29CQUFldUIsYUFBYTtnQkFBeUQ7Z0JBQzdGO29CQUFFdkIsTUFBTTtvQkFBYXVCLGFBQWE7Z0JBQXNDO2dCQUN4RTtvQkFBRXZCLE1BQU07b0JBQWF1QixhQUFhO2dCQUF1QztnQkFDekU7b0JBQUV2QixNQUFNO29CQUFXdUIsYUFBYTtnQkFBdUM7Z0JBQ3ZFO29CQUFFdkIsTUFBTTtvQkFBVXVCLGFBQWE7Z0JBQStCO2dCQUM5RDtvQkFBRXZCLE1BQU07b0JBQWlCdUIsYUFBYTtnQkFBdUM7Z0JBQzdFO29CQUFFdkIsTUFBTTtvQkFBZXVCLGFBQWE7Z0JBQStDO2dCQUNuRjtvQkFBRXZCLE1BQU07b0JBQVl1QixhQUFhO2dCQUFvQzthQUN0RTtZQUVERCxrQkFBa0JFLE9BQU8sQ0FBQ0MsQ0FBQUE7Z0JBQ3hCLElBQUksQ0FBQ1IsV0FBVyxDQUFDO29CQUFFLEdBQUdRLEdBQUc7b0JBQUUvRSxRQUFRO2dCQUFLO1lBQzFDO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvaW52ZW50b3J5LXN0b3JhZ2UudHM/NjQ1OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBsb2NhbFN0b3JhZ2UtYmFzZWQgaW52ZW50b3J5IG1hbmFnZW1lbnQgc3lzdGVtXG5cbmV4cG9ydCBpbnRlcmZhY2UgU3VwcGxpZXIge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBjb250YWN0X3BlcnNvbj86IHN0cmluZ1xuICBwaG9uZT86IHN0cmluZ1xuICBlbWFpbD86IHN0cmluZ1xuICBhZGRyZXNzPzogc3RyaW5nXG4gIHRheF9udW1iZXI/OiBzdHJpbmdcbiAgcGF5bWVudF90ZXJtczogJ2Nhc2gnIHwgJ2NyZWRpdF8zMCcgfCAnY3JlZGl0XzYwJyB8ICdjcmVkaXRfOTAnXG4gIGNyZWRpdF9saW1pdDogbnVtYmVyXG4gIGN1cnJlbnRfYmFsYW5jZTogbnVtYmVyIC8vIE91dHN0YW5kaW5nIGJhbGFuY2UgZm9yIGNyZWRpdCBzdXBwbGllcnNcbiAgbm90ZXM/OiBzdHJpbmdcbiAgYWN0aXZlOiBib29sZWFuXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQdXJjaGFzZSB7XG4gIGlkOiBzdHJpbmdcbiAgc3VwcGxpZXJfaWQ6IHN0cmluZ1xuICBpbnZvaWNlX251bWJlcj86IHN0cmluZ1xuICBwdXJjaGFzZV9kYXRlOiBzdHJpbmdcbiAgcGF5bWVudF90eXBlOiAnY2FzaCcgfCAnY3JlZGl0J1xuICB0b3RhbF9hbW91bnQ6IG51bWJlclxuICBwYWlkX2Ftb3VudDogbnVtYmVyXG4gIHJlbWFpbmluZ19iYWxhbmNlOiBudW1iZXJcbiAgcGF5bWVudF9zdGF0dXM6ICdwYWlkJyB8ICdwYXJ0aWFsJyB8ICd1bnBhaWQnXG4gIGR1ZV9kYXRlPzogc3RyaW5nXG4gIG5vdGVzPzogc3RyaW5nXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBQdXJjaGFzZUl0ZW0ge1xuICBpZDogc3RyaW5nXG4gIHB1cmNoYXNlX2lkOiBzdHJpbmdcbiAgcHJvZHVjdF9pZDogc3RyaW5nXG4gIHByb2R1Y3RfbmFtZTogc3RyaW5nXG4gIHF1YW50aXR5OiBudW1iZXJcbiAgdW5pdF9jb3N0OiBudW1iZXJcbiAgdG90YWxfY29zdDogbnVtYmVyXG4gIGV4cGlyeV9kYXRlPzogc3RyaW5nXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFN1cHBsaWVyUGF5bWVudCB7XG4gIGlkOiBzdHJpbmdcbiAgc3VwcGxpZXJfaWQ6IHN0cmluZ1xuICBwdXJjaGFzZV9pZD86IHN0cmluZ1xuICBwYXltZW50X2RhdGU6IHN0cmluZ1xuICBhbW91bnQ6IG51bWJlclxuICBwYXltZW50X21ldGhvZDogJ2Nhc2gnIHwgJ2JhbmtfdHJhbnNmZXInIHwgJ2NoZWNrJyB8ICdjYXJkJ1xuICByZWZlcmVuY2VfbnVtYmVyPzogc3RyaW5nXG4gIG5vdGVzPzogc3RyaW5nXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFN0b2NrTW92ZW1lbnQge1xuICBpZDogc3RyaW5nXG4gIHByb2R1Y3RfaWQ6IHN0cmluZ1xuICBwcm9kdWN0X25hbWU6IHN0cmluZ1xuICBtb3ZlbWVudF90eXBlOiAncHVyY2hhc2UnIHwgJ3NhbGUnIHwgJ2FkanVzdG1lbnQnIHwgJ3JldHVybicgfCAnZXhwaXJlZCdcbiAgcXVhbnRpdHlfY2hhbmdlOiBudW1iZXIgLy8gUG9zaXRpdmUgZm9yIGFkZGl0aW9ucywgbmVnYXRpdmUgZm9yIHJlZHVjdGlvbnNcbiAgcHJldmlvdXNfc3RvY2s6IG51bWJlclxuICBuZXdfc3RvY2s6IG51bWJlclxuICByZWZlcmVuY2VfaWQ/OiBzdHJpbmcgLy8gQ2FuIHJlZmVyZW5jZSBwdXJjaGFzZV9pZCwgc2FsZV9pZCwgZXRjLlxuICByZWZlcmVuY2VfdHlwZT86IHN0cmluZyAvLyAncHVyY2hhc2UnLCAnc2FsZScsICdtYW51YWxfYWRqdXN0bWVudCcsIGV0Yy5cbiAgbm90ZXM/OiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ2F0ZWdvcnkge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICBwYXJlbnRfaWQ/OiBzdHJpbmdcbiAgYWN0aXZlOiBib29sZWFuXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBFeHRlbmRlZFByb2R1Y3Qge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBjYXRlZ29yeTogc3RyaW5nXG4gIHByaWNlX2R6ZDogbnVtYmVyXG4gIHN0b2NrOiBudW1iZXJcbiAgbWluX3N0b2NrOiBudW1iZXJcbiAgZXhwaXJ5X2RhdGU/OiBzdHJpbmdcbiAgaW1hZ2VfdXJsPzogc3RyaW5nXG4gIGJhcmNvZGU/OiBzdHJpbmdcbiAgcXJfY29kZT86IHN0cmluZ1xuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICBicmFuZD86IHN0cmluZ1xuICB1bml0OiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xufVxuXG4vLyBsb2NhbFN0b3JhZ2Uga2V5c1xuY29uc3QgU1RPUkFHRV9LRVlTID0ge1xuICBTVVBQTElFUlM6ICdneW1fc3VwcGxpZXJzJyxcbiAgUFVSQ0hBU0VTOiAnZ3ltX3B1cmNoYXNlcycsXG4gIFBVUkNIQVNFX0lURU1TOiAnZ3ltX3B1cmNoYXNlX2l0ZW1zJyxcbiAgU1VQUExJRVJfUEFZTUVOVFM6ICdneW1fc3VwcGxpZXJfcGF5bWVudHMnLFxuICBTVE9DS19NT1ZFTUVOVFM6ICdneW1fc3RvY2tfbW92ZW1lbnRzJyxcbiAgQ0FURUdPUklFUzogJ2d5bV9jYXRlZ29yaWVzJyxcbiAgUFJPRFVDVFM6ICdneW1fcHJvZHVjdHMnLCAvLyBFeHRlbmRlZCBwcm9kdWN0IHN0cnVjdHVyZVxufSBhcyBjb25zdFxuXG4vLyBVdGlsaXR5IGZ1bmN0aW9ucyBmb3IgbG9jYWxTdG9yYWdlIG9wZXJhdGlvbnNcbmV4cG9ydCBjbGFzcyBJbnZlbnRvcnlTdG9yYWdlIHtcbiAgLy8gR2VuZXJpYyBzdG9yYWdlIG9wZXJhdGlvbnNcbiAgc3RhdGljIGdldEZyb21TdG9yYWdlPFQ+KGtleTogc3RyaW5nKTogVFtdIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSlcbiAgICAgIHJldHVybiBkYXRhID8gSlNPTi5wYXJzZShkYXRhKSA6IFtdXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIHJlYWRpbmcgJHtrZXl9IGZyb20gbG9jYWxTdG9yYWdlOmAsIGVycm9yKVxuICAgICAgcmV0dXJuIFtdXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIHNhdmVUb1N0b3JhZ2U8VD4oa2V5OiBzdHJpbmcsIGRhdGE6IFRbXSk6IHZvaWQge1xuICAgIHRyeSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShrZXksIEpTT04uc3RyaW5naWZ5KGRhdGEpKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBzYXZpbmcgJHtrZXl9IHRvIGxvY2FsU3RvcmFnZTpgLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XG4gICAgcmV0dXJuIERhdGUubm93KCkudG9TdHJpbmcoMzYpICsgTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIpXG4gIH1cblxuICAvLyBTdXBwbGllciBvcGVyYXRpb25zXG4gIHN0YXRpYyBnZXRTdXBwbGllcnMoKTogU3VwcGxpZXJbXSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0RnJvbVN0b3JhZ2U8U3VwcGxpZXI+KFNUT1JBR0VfS0VZUy5TVVBQTElFUlMpXG4gIH1cblxuICBzdGF0aWMgc2F2ZVN1cHBsaWVycyhzdXBwbGllcnM6IFN1cHBsaWVyW10pOiB2b2lkIHtcbiAgICB0aGlzLnNhdmVUb1N0b3JhZ2UoU1RPUkFHRV9LRVlTLlNVUFBMSUVSUywgc3VwcGxpZXJzKVxuICB9XG5cbiAgc3RhdGljIGFkZFN1cHBsaWVyKHN1cHBsaWVyOiBPbWl0PFN1cHBsaWVyLCAnaWQnIHwgJ2NyZWF0ZWRfYXQnIHwgJ3VwZGF0ZWRfYXQnPik6IFN1cHBsaWVyIHtcbiAgICBjb25zdCBzdXBwbGllcnMgPSB0aGlzLmdldFN1cHBsaWVycygpXG4gICAgY29uc3QgbmV3U3VwcGxpZXI6IFN1cHBsaWVyID0ge1xuICAgICAgLi4uc3VwcGxpZXIsXG4gICAgICBpZDogdGhpcy5nZW5lcmF0ZUlkKCksXG4gICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgfVxuICAgIHN1cHBsaWVycy5wdXNoKG5ld1N1cHBsaWVyKVxuICAgIHRoaXMuc2F2ZVN1cHBsaWVycyhzdXBwbGllcnMpXG4gICAgcmV0dXJuIG5ld1N1cHBsaWVyXG4gIH1cblxuICBzdGF0aWMgdXBkYXRlU3VwcGxpZXIoaWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxTdXBwbGllcj4pOiBTdXBwbGllciB8IG51bGwge1xuICAgIGNvbnN0IHN1cHBsaWVycyA9IHRoaXMuZ2V0U3VwcGxpZXJzKClcbiAgICBjb25zdCBpbmRleCA9IHN1cHBsaWVycy5maW5kSW5kZXgocyA9PiBzLmlkID09PSBpZClcbiAgICBpZiAoaW5kZXggPT09IC0xKSByZXR1cm4gbnVsbFxuXG4gICAgc3VwcGxpZXJzW2luZGV4XSA9IHtcbiAgICAgIC4uLnN1cHBsaWVyc1tpbmRleF0sXG4gICAgICAuLi51cGRhdGVzLFxuICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIH1cbiAgICB0aGlzLnNhdmVTdXBwbGllcnMoc3VwcGxpZXJzKVxuICAgIHJldHVybiBzdXBwbGllcnNbaW5kZXhdXG4gIH1cblxuICBzdGF0aWMgZGVsZXRlU3VwcGxpZXIoaWQ6IHN0cmluZyk6IGJvb2xlYW4ge1xuICAgIGNvbnN0IHN1cHBsaWVycyA9IHRoaXMuZ2V0U3VwcGxpZXJzKClcbiAgICBjb25zdCBpbmRleCA9IHN1cHBsaWVycy5maW5kSW5kZXgocyA9PiBzLmlkID09PSBpZClcbiAgICBpZiAoaW5kZXggPT09IC0xKSByZXR1cm4gZmFsc2VcblxuICAgIHN1cHBsaWVyc1tpbmRleF0uYWN0aXZlID0gZmFsc2VcbiAgICB0aGlzLnNhdmVTdXBwbGllcnMoc3VwcGxpZXJzKVxuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICBzdGF0aWMgZ2V0U3VwcGxpZXJCeUlkKGlkOiBzdHJpbmcpOiBTdXBwbGllciB8IG51bGwge1xuICAgIGNvbnN0IHN1cHBsaWVycyA9IHRoaXMuZ2V0U3VwcGxpZXJzKClcbiAgICByZXR1cm4gc3VwcGxpZXJzLmZpbmQocyA9PiBzLmlkID09PSBpZCkgfHwgbnVsbFxuICB9XG5cbiAgLy8gUHVyY2hhc2Ugb3BlcmF0aW9uc1xuICBzdGF0aWMgZ2V0UHVyY2hhc2VzKCk6IFB1cmNoYXNlW10ge1xuICAgIHJldHVybiB0aGlzLmdldEZyb21TdG9yYWdlPFB1cmNoYXNlPihTVE9SQUdFX0tFWVMuUFVSQ0hBU0VTKVxuICB9XG5cbiAgc3RhdGljIHNhdmVQdXJjaGFzZXMocHVyY2hhc2VzOiBQdXJjaGFzZVtdKTogdm9pZCB7XG4gICAgdGhpcy5zYXZlVG9TdG9yYWdlKFNUT1JBR0VfS0VZUy5QVVJDSEFTRVMsIHB1cmNoYXNlcylcbiAgfVxuXG4gIHN0YXRpYyBhZGRQdXJjaGFzZShwdXJjaGFzZTogT21pdDxQdXJjaGFzZSwgJ2lkJyB8ICdjcmVhdGVkX2F0JyB8ICd1cGRhdGVkX2F0Jz4pOiBQdXJjaGFzZSB7XG4gICAgY29uc3QgcHVyY2hhc2VzID0gdGhpcy5nZXRQdXJjaGFzZXMoKVxuICAgIGNvbnN0IG5ld1B1cmNoYXNlOiBQdXJjaGFzZSA9IHtcbiAgICAgIC4uLnB1cmNoYXNlLFxuICAgICAgaWQ6IHRoaXMuZ2VuZXJhdGVJZCgpLFxuICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIH1cbiAgICBwdXJjaGFzZXMucHVzaChuZXdQdXJjaGFzZSlcbiAgICB0aGlzLnNhdmVQdXJjaGFzZXMocHVyY2hhc2VzKVxuXG4gICAgLy8gVXBkYXRlIHN1cHBsaWVyIGJhbGFuY2UgaWYgY3JlZGl0IHB1cmNoYXNlXG4gICAgaWYgKHB1cmNoYXNlLnBheW1lbnRfdHlwZSA9PT0gJ2NyZWRpdCcpIHtcbiAgICAgIHRoaXMudXBkYXRlU3VwcGxpZXJCYWxhbmNlKHB1cmNoYXNlLnN1cHBsaWVyX2lkLCBwdXJjaGFzZS50b3RhbF9hbW91bnQpXG4gICAgfVxuXG4gICAgcmV0dXJuIG5ld1B1cmNoYXNlXG4gIH1cblxuICBzdGF0aWMgdXBkYXRlUHVyY2hhc2UoaWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxQdXJjaGFzZT4pOiBQdXJjaGFzZSB8IG51bGwge1xuICAgIGNvbnN0IHB1cmNoYXNlcyA9IHRoaXMuZ2V0UHVyY2hhc2VzKClcbiAgICBjb25zdCBpbmRleCA9IHB1cmNoYXNlcy5maW5kSW5kZXgocCA9PiBwLmlkID09PSBpZClcbiAgICBpZiAoaW5kZXggPT09IC0xKSByZXR1cm4gbnVsbFxuXG4gICAgcHVyY2hhc2VzW2luZGV4XSA9IHtcbiAgICAgIC4uLnB1cmNoYXNlc1tpbmRleF0sXG4gICAgICAuLi51cGRhdGVzLFxuICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIH1cbiAgICB0aGlzLnNhdmVQdXJjaGFzZXMocHVyY2hhc2VzKVxuICAgIHJldHVybiBwdXJjaGFzZXNbaW5kZXhdXG4gIH1cblxuICAvLyBQdXJjaGFzZSBJdGVtcyBvcGVyYXRpb25zXG4gIHN0YXRpYyBnZXRQdXJjaGFzZUl0ZW1zKCk6IFB1cmNoYXNlSXRlbVtdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRGcm9tU3RvcmFnZTxQdXJjaGFzZUl0ZW0+KFNUT1JBR0VfS0VZUy5QVVJDSEFTRV9JVEVNUylcbiAgfVxuXG4gIHN0YXRpYyBzYXZlUHVyY2hhc2VJdGVtcyhpdGVtczogUHVyY2hhc2VJdGVtW10pOiB2b2lkIHtcbiAgICB0aGlzLnNhdmVUb1N0b3JhZ2UoU1RPUkFHRV9LRVlTLlBVUkNIQVNFX0lURU1TLCBpdGVtcylcbiAgfVxuXG4gIHN0YXRpYyBhZGRQdXJjaGFzZUl0ZW0oaXRlbTogT21pdDxQdXJjaGFzZUl0ZW0sICdpZCcgfCAnY3JlYXRlZF9hdCc+KTogUHVyY2hhc2VJdGVtIHtcbiAgICBjb25zdCBpdGVtcyA9IHRoaXMuZ2V0UHVyY2hhc2VJdGVtcygpXG4gICAgY29uc3QgbmV3SXRlbTogUHVyY2hhc2VJdGVtID0ge1xuICAgICAgLi4uaXRlbSxcbiAgICAgIGlkOiB0aGlzLmdlbmVyYXRlSWQoKSxcbiAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICB9XG4gICAgaXRlbXMucHVzaChuZXdJdGVtKVxuICAgIHRoaXMuc2F2ZVB1cmNoYXNlSXRlbXMoaXRlbXMpXG5cbiAgICAvLyBVcGRhdGUgcHJvZHVjdCBzdG9ja1xuICAgIHRoaXMudXBkYXRlUHJvZHVjdFN0b2NrKGl0ZW0ucHJvZHVjdF9pZCwgaXRlbS5xdWFudGl0eSwgJ3B1cmNoYXNlJywgbmV3SXRlbS5pZClcblxuICAgIHJldHVybiBuZXdJdGVtXG4gIH1cblxuICBzdGF0aWMgZ2V0UHVyY2hhc2VJdGVtc0J5UHVyY2hhc2VJZChwdXJjaGFzZUlkOiBzdHJpbmcpOiBQdXJjaGFzZUl0ZW1bXSB7XG4gICAgY29uc3QgaXRlbXMgPSB0aGlzLmdldFB1cmNoYXNlSXRlbXMoKVxuICAgIHJldHVybiBpdGVtcy5maWx0ZXIoaXRlbSA9PiBpdGVtLnB1cmNoYXNlX2lkID09PSBwdXJjaGFzZUlkKVxuICB9XG5cbiAgLy8gU3VwcGxpZXIgUGF5bWVudCBvcGVyYXRpb25zXG4gIHN0YXRpYyBnZXRTdXBwbGllclBheW1lbnRzKCk6IFN1cHBsaWVyUGF5bWVudFtdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRGcm9tU3RvcmFnZTxTdXBwbGllclBheW1lbnQ+KFNUT1JBR0VfS0VZUy5TVVBQTElFUl9QQVlNRU5UUylcbiAgfVxuXG4gIHN0YXRpYyBzYXZlU3VwcGxpZXJQYXltZW50cyhwYXltZW50czogU3VwcGxpZXJQYXltZW50W10pOiB2b2lkIHtcbiAgICB0aGlzLnNhdmVUb1N0b3JhZ2UoU1RPUkFHRV9LRVlTLlNVUFBMSUVSX1BBWU1FTlRTLCBwYXltZW50cylcbiAgfVxuXG4gIHN0YXRpYyBhZGRTdXBwbGllclBheW1lbnQocGF5bWVudDogT21pdDxTdXBwbGllclBheW1lbnQsICdpZCcgfCAnY3JlYXRlZF9hdCc+KTogU3VwcGxpZXJQYXltZW50IHtcbiAgICBjb25zdCBwYXltZW50cyA9IHRoaXMuZ2V0U3VwcGxpZXJQYXltZW50cygpXG4gICAgY29uc3QgbmV3UGF5bWVudDogU3VwcGxpZXJQYXltZW50ID0ge1xuICAgICAgLi4ucGF5bWVudCxcbiAgICAgIGlkOiB0aGlzLmdlbmVyYXRlSWQoKSxcbiAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICB9XG4gICAgcGF5bWVudHMucHVzaChuZXdQYXltZW50KVxuICAgIHRoaXMuc2F2ZVN1cHBsaWVyUGF5bWVudHMocGF5bWVudHMpXG5cbiAgICAvLyBVcGRhdGUgc3VwcGxpZXIgYmFsYW5jZVxuICAgIHRoaXMudXBkYXRlU3VwcGxpZXJCYWxhbmNlKHBheW1lbnQuc3VwcGxpZXJfaWQsIC1wYXltZW50LmFtb3VudClcblxuICAgIC8vIFVwZGF0ZSBwdXJjaGFzZSBwYXltZW50IHN0YXR1cyBpZiBwdXJjaGFzZV9pZCBwcm92aWRlZFxuICAgIGlmIChwYXltZW50LnB1cmNoYXNlX2lkKSB7XG4gICAgICB0aGlzLnVwZGF0ZVB1cmNoYXNlUGF5bWVudFN0YXR1cyhwYXltZW50LnB1cmNoYXNlX2lkLCBwYXltZW50LmFtb3VudClcbiAgICB9XG5cbiAgICByZXR1cm4gbmV3UGF5bWVudFxuICB9XG5cbiAgc3RhdGljIGdldFN1cHBsaWVyUGF5bWVudHNCeVN1cHBsaWVyKHN1cHBsaWVySWQ6IHN0cmluZyk6IFN1cHBsaWVyUGF5bWVudFtdIHtcbiAgICBjb25zdCBwYXltZW50cyA9IHRoaXMuZ2V0U3VwcGxpZXJQYXltZW50cygpXG4gICAgcmV0dXJuIHBheW1lbnRzLmZpbHRlcihwYXltZW50ID0+IHBheW1lbnQuc3VwcGxpZXJfaWQgPT09IHN1cHBsaWVySWQpXG4gIH1cblxuICAvLyBIZWxwZXIgbWV0aG9kc1xuICBwcml2YXRlIHN0YXRpYyB1cGRhdGVTdXBwbGllckJhbGFuY2Uoc3VwcGxpZXJJZDogc3RyaW5nLCBhbW91bnQ6IG51bWJlcik6IHZvaWQge1xuICAgIGNvbnN0IHN1cHBsaWVycyA9IHRoaXMuZ2V0U3VwcGxpZXJzKClcbiAgICBjb25zdCBpbmRleCA9IHN1cHBsaWVycy5maW5kSW5kZXgocyA9PiBzLmlkID09PSBzdXBwbGllcklkKVxuICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgIHN1cHBsaWVyc1tpbmRleF0uY3VycmVudF9iYWxhbmNlICs9IGFtb3VudFxuICAgICAgc3VwcGxpZXJzW2luZGV4XS51cGRhdGVkX2F0ID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB0aGlzLnNhdmVTdXBwbGllcnMoc3VwcGxpZXJzKVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIHVwZGF0ZVB1cmNoYXNlUGF5bWVudFN0YXR1cyhwdXJjaGFzZUlkOiBzdHJpbmcsIHBheW1lbnRBbW91bnQ6IG51bWJlcik6IHZvaWQge1xuICAgIGNvbnN0IHB1cmNoYXNlcyA9IHRoaXMuZ2V0UHVyY2hhc2VzKClcbiAgICBjb25zdCBpbmRleCA9IHB1cmNoYXNlcy5maW5kSW5kZXgocCA9PiBwLmlkID09PSBwdXJjaGFzZUlkKVxuICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgIGNvbnN0IHB1cmNoYXNlID0gcHVyY2hhc2VzW2luZGV4XVxuICAgICAgcHVyY2hhc2UucGFpZF9hbW91bnQgKz0gcGF5bWVudEFtb3VudFxuICAgICAgcHVyY2hhc2UucmVtYWluaW5nX2JhbGFuY2UgPSBwdXJjaGFzZS50b3RhbF9hbW91bnQgLSBwdXJjaGFzZS5wYWlkX2Ftb3VudFxuICAgICAgXG4gICAgICBpZiAocHVyY2hhc2UucmVtYWluaW5nX2JhbGFuY2UgPD0gMCkge1xuICAgICAgICBwdXJjaGFzZS5wYXltZW50X3N0YXR1cyA9ICdwYWlkJ1xuICAgICAgfSBlbHNlIGlmIChwdXJjaGFzZS5wYWlkX2Ftb3VudCA+IDApIHtcbiAgICAgICAgcHVyY2hhc2UucGF5bWVudF9zdGF0dXMgPSAncGFydGlhbCdcbiAgICAgIH1cbiAgICAgIFxuICAgICAgcHVyY2hhc2UudXBkYXRlZF9hdCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgdGhpcy5zYXZlUHVyY2hhc2VzKHB1cmNoYXNlcylcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyB1cGRhdGVQcm9kdWN0U3RvY2socHJvZHVjdElkOiBzdHJpbmcsIHF1YW50aXR5OiBudW1iZXIsIG1vdmVtZW50VHlwZTogU3RvY2tNb3ZlbWVudFsnbW92ZW1lbnRfdHlwZSddLCByZWZlcmVuY2VJZDogc3RyaW5nKTogdm9pZCB7XG4gICAgLy8gR2V0IGN1cnJlbnQgcHJvZHVjdHMgKGFzc3VtaW5nIHRoZXkncmUgc3RvcmVkIGluIGxvY2FsU3RvcmFnZSlcbiAgICBjb25zdCBwcm9kdWN0cyA9IHRoaXMuZ2V0RnJvbVN0b3JhZ2U8RXh0ZW5kZWRQcm9kdWN0PihTVE9SQUdFX0tFWVMuUFJPRFVDVFMpXG4gICAgY29uc3QgcHJvZHVjdEluZGV4ID0gcHJvZHVjdHMuZmluZEluZGV4KHAgPT4gcC5pZCA9PT0gcHJvZHVjdElkKVxuICAgIFxuICAgIGlmIChwcm9kdWN0SW5kZXggIT09IC0xKSB7XG4gICAgICBjb25zdCBwcm9kdWN0ID0gcHJvZHVjdHNbcHJvZHVjdEluZGV4XVxuICAgICAgY29uc3QgcHJldmlvdXNTdG9jayA9IHByb2R1Y3Quc3RvY2tcbiAgICAgIGNvbnN0IG5ld1N0b2NrID0gcHJldmlvdXNTdG9jayArIHF1YW50aXR5XG4gICAgICBcbiAgICAgIC8vIFVwZGF0ZSBwcm9kdWN0IHN0b2NrXG4gICAgICBwcm9kdWN0LnN0b2NrID0gbmV3U3RvY2tcbiAgICAgIHByb2R1Y3QudXBkYXRlZF9hdCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgdGhpcy5zYXZlVG9TdG9yYWdlKFNUT1JBR0VfS0VZUy5QUk9EVUNUUywgcHJvZHVjdHMpXG4gICAgICBcbiAgICAgIC8vIENyZWF0ZSBzdG9jayBtb3ZlbWVudCByZWNvcmRcbiAgICAgIHRoaXMuYWRkU3RvY2tNb3ZlbWVudCh7XG4gICAgICAgIHByb2R1Y3RfaWQ6IHByb2R1Y3RJZCxcbiAgICAgICAgcHJvZHVjdF9uYW1lOiBwcm9kdWN0Lm5hbWUsXG4gICAgICAgIG1vdmVtZW50X3R5cGU6IG1vdmVtZW50VHlwZSxcbiAgICAgICAgcXVhbnRpdHlfY2hhbmdlOiBxdWFudGl0eSxcbiAgICAgICAgcHJldmlvdXNfc3RvY2s6IHByZXZpb3VzU3RvY2ssXG4gICAgICAgIG5ld19zdG9jazogbmV3U3RvY2ssXG4gICAgICAgIHJlZmVyZW5jZV9pZDogcmVmZXJlbmNlSWQsXG4gICAgICAgIHJlZmVyZW5jZV90eXBlOiBtb3ZlbWVudFR5cGUsXG4gICAgICAgIG5vdGVzOiBgU3RvY2sgJHttb3ZlbWVudFR5cGV9IC0gJHtxdWFudGl0eX0gdW5pdHNgLFxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICAvLyBTdG9jayBNb3ZlbWVudCBvcGVyYXRpb25zXG4gIHN0YXRpYyBnZXRTdG9ja01vdmVtZW50cygpOiBTdG9ja01vdmVtZW50W10ge1xuICAgIHJldHVybiB0aGlzLmdldEZyb21TdG9yYWdlPFN0b2NrTW92ZW1lbnQ+KFNUT1JBR0VfS0VZUy5TVE9DS19NT1ZFTUVOVFMpXG4gIH1cblxuICBzdGF0aWMgc2F2ZVN0b2NrTW92ZW1lbnRzKG1vdmVtZW50czogU3RvY2tNb3ZlbWVudFtdKTogdm9pZCB7XG4gICAgdGhpcy5zYXZlVG9TdG9yYWdlKFNUT1JBR0VfS0VZUy5TVE9DS19NT1ZFTUVOVFMsIG1vdmVtZW50cylcbiAgfVxuXG4gIHN0YXRpYyBhZGRTdG9ja01vdmVtZW50KG1vdmVtZW50OiBPbWl0PFN0b2NrTW92ZW1lbnQsICdpZCcgfCAnY3JlYXRlZF9hdCc+KTogU3RvY2tNb3ZlbWVudCB7XG4gICAgY29uc3QgbW92ZW1lbnRzID0gdGhpcy5nZXRTdG9ja01vdmVtZW50cygpXG4gICAgY29uc3QgbmV3TW92ZW1lbnQ6IFN0b2NrTW92ZW1lbnQgPSB7XG4gICAgICAuLi5tb3ZlbWVudCxcbiAgICAgIGlkOiB0aGlzLmdlbmVyYXRlSWQoKSxcbiAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICB9XG4gICAgbW92ZW1lbnRzLnB1c2gobmV3TW92ZW1lbnQpXG4gICAgdGhpcy5zYXZlU3RvY2tNb3ZlbWVudHMobW92ZW1lbnRzKVxuICAgIHJldHVybiBuZXdNb3ZlbWVudFxuICB9XG5cbiAgc3RhdGljIGdldFN0b2NrTW92ZW1lbnRzQnlQcm9kdWN0KHByb2R1Y3RJZDogc3RyaW5nKTogU3RvY2tNb3ZlbWVudFtdIHtcbiAgICBjb25zdCBtb3ZlbWVudHMgPSB0aGlzLmdldFN0b2NrTW92ZW1lbnRzKClcbiAgICByZXR1cm4gbW92ZW1lbnRzLmZpbHRlcihtb3ZlbWVudCA9PiBtb3ZlbWVudC5wcm9kdWN0X2lkID09PSBwcm9kdWN0SWQpXG4gIH1cblxuICAvLyBDYXRlZ29yeSBvcGVyYXRpb25zXG4gIHN0YXRpYyBnZXRDYXRlZ29yaWVzKCk6IENhdGVnb3J5W10ge1xuICAgIHJldHVybiB0aGlzLmdldEZyb21TdG9yYWdlPENhdGVnb3J5PihTVE9SQUdFX0tFWVMuQ0FURUdPUklFUylcbiAgfVxuXG4gIHN0YXRpYyBzYXZlQ2F0ZWdvcmllcyhjYXRlZ29yaWVzOiBDYXRlZ29yeVtdKTogdm9pZCB7XG4gICAgdGhpcy5zYXZlVG9TdG9yYWdlKFNUT1JBR0VfS0VZUy5DQVRFR09SSUVTLCBjYXRlZ29yaWVzKVxuICB9XG5cbiAgc3RhdGljIGFkZENhdGVnb3J5KGNhdGVnb3J5OiBPbWl0PENhdGVnb3J5LCAnaWQnIHwgJ2NyZWF0ZWRfYXQnIHwgJ3VwZGF0ZWRfYXQnPik6IENhdGVnb3J5IHtcbiAgICBjb25zdCBjYXRlZ29yaWVzID0gdGhpcy5nZXRDYXRlZ29yaWVzKClcbiAgICBjb25zdCBuZXdDYXRlZ29yeTogQ2F0ZWdvcnkgPSB7XG4gICAgICAuLi5jYXRlZ29yeSxcbiAgICAgIGlkOiB0aGlzLmdlbmVyYXRlSWQoKSxcbiAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICB9XG4gICAgY2F0ZWdvcmllcy5wdXNoKG5ld0NhdGVnb3J5KVxuICAgIHRoaXMuc2F2ZUNhdGVnb3JpZXMoY2F0ZWdvcmllcylcbiAgICByZXR1cm4gbmV3Q2F0ZWdvcnlcbiAgfVxuXG4gIC8vIEluaXRpYWxpemUgZGVmYXVsdCBkYXRhXG4gIHN0YXRpYyBpbml0aWFsaXplRGVmYXVsdERhdGEoKTogdm9pZCB7XG4gICAgLy8gSW5pdGlhbGl6ZSBkZWZhdWx0IGNhdGVnb3JpZXMgaWYgbm9uZSBleGlzdFxuICAgIGNvbnN0IGNhdGVnb3JpZXMgPSB0aGlzLmdldENhdGVnb3JpZXMoKVxuICAgIGlmIChjYXRlZ29yaWVzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgY29uc3QgZGVmYXVsdENhdGVnb3JpZXMgPSBbXG4gICAgICAgIHsgbmFtZTogJ1N1cHBsZW1lbnRzJywgZGVzY3JpcHRpb246ICdQcm90ZWluIHBvd2RlcnMsIHZpdGFtaW5zLCBhbmQgbnV0cml0aW9uYWwgc3VwcGxlbWVudHMnIH0sXG4gICAgICAgIHsgbmFtZTogJ0JldmVyYWdlcycsIGRlc2NyaXB0aW9uOiAnRW5lcmd5IGRyaW5rcywgd2F0ZXIsIHNwb3J0cyBkcmlua3MnIH0sXG4gICAgICAgIHsgbmFtZTogJ0VxdWlwbWVudCcsIGRlc2NyaXB0aW9uOiAnR3ltIGVxdWlwbWVudCwgYWNjZXNzb3JpZXMsIGFuZCBnZWFyJyB9LFxuICAgICAgICB7IG5hbWU6ICdBcHBhcmVsJywgZGVzY3JpcHRpb246ICdHeW0gY2xvdGhpbmcsIHNob2VzLCBhbmQgYWNjZXNzb3JpZXMnIH0sXG4gICAgICAgIHsgbmFtZTogJ1NuYWNrcycsIGRlc2NyaXB0aW9uOiAnUHJvdGVpbiBiYXJzLCBoZWFsdGh5IHNuYWNrcycgfSxcbiAgICAgICAgeyBuYW1lOiAnUGVyc29uYWwgQ2FyZScsIGRlc2NyaXB0aW9uOiAnVG93ZWxzLCB0b2lsZXRyaWVzLCBoeWdpZW5lIHByb2R1Y3RzJyB9LFxuICAgICAgICB7IG5hbWU6ICdBY2Nlc3NvcmllcycsIGRlc2NyaXB0aW9uOiAnR2xvdmVzLCBiZWx0cywgc3RyYXBzLCBhbmQgb3RoZXIgYWNjZXNzb3JpZXMnIH0sXG4gICAgICAgIHsgbmFtZTogJ1JlY292ZXJ5JywgZGVzY3JpcHRpb246ICdSZWNvdmVyeSB0b29scywgbWFzc2FnZSBlcXVpcG1lbnQnIH0sXG4gICAgICBdXG5cbiAgICAgIGRlZmF1bHRDYXRlZ29yaWVzLmZvckVhY2goY2F0ID0+IHtcbiAgICAgICAgdGhpcy5hZGRDYXRlZ29yeSh7IC4uLmNhdCwgYWN0aXZlOiB0cnVlIH0pXG4gICAgICB9KVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbIlNUT1JBR0VfS0VZUyIsIlNVUFBMSUVSUyIsIlBVUkNIQVNFUyIsIlBVUkNIQVNFX0lURU1TIiwiU1VQUExJRVJfUEFZTUVOVFMiLCJTVE9DS19NT1ZFTUVOVFMiLCJDQVRFR09SSUVTIiwiUFJPRFVDVFMiLCJJbnZlbnRvcnlTdG9yYWdlIiwiZ2V0RnJvbVN0b3JhZ2UiLCJrZXkiLCJkYXRhIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsInNhdmVUb1N0b3JhZ2UiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiZ2VuZXJhdGVJZCIsIkRhdGUiLCJub3ciLCJ0b1N0cmluZyIsIk1hdGgiLCJyYW5kb20iLCJzdWJzdHIiLCJnZXRTdXBwbGllcnMiLCJzYXZlU3VwcGxpZXJzIiwic3VwcGxpZXJzIiwiYWRkU3VwcGxpZXIiLCJzdXBwbGllciIsIm5ld1N1cHBsaWVyIiwiaWQiLCJjcmVhdGVkX2F0IiwidG9JU09TdHJpbmciLCJ1cGRhdGVkX2F0IiwicHVzaCIsInVwZGF0ZVN1cHBsaWVyIiwidXBkYXRlcyIsImluZGV4IiwiZmluZEluZGV4IiwicyIsImRlbGV0ZVN1cHBsaWVyIiwiYWN0aXZlIiwiZ2V0U3VwcGxpZXJCeUlkIiwiZmluZCIsImdldFB1cmNoYXNlcyIsInNhdmVQdXJjaGFzZXMiLCJwdXJjaGFzZXMiLCJhZGRQdXJjaGFzZSIsInB1cmNoYXNlIiwibmV3UHVyY2hhc2UiLCJwYXltZW50X3R5cGUiLCJ1cGRhdGVTdXBwbGllckJhbGFuY2UiLCJzdXBwbGllcl9pZCIsInRvdGFsX2Ftb3VudCIsInVwZGF0ZVB1cmNoYXNlIiwicCIsImdldFB1cmNoYXNlSXRlbXMiLCJzYXZlUHVyY2hhc2VJdGVtcyIsIml0ZW1zIiwiYWRkUHVyY2hhc2VJdGVtIiwiaXRlbSIsIm5ld0l0ZW0iLCJ1cGRhdGVQcm9kdWN0U3RvY2siLCJwcm9kdWN0X2lkIiwicXVhbnRpdHkiLCJnZXRQdXJjaGFzZUl0ZW1zQnlQdXJjaGFzZUlkIiwicHVyY2hhc2VJZCIsImZpbHRlciIsInB1cmNoYXNlX2lkIiwiZ2V0U3VwcGxpZXJQYXltZW50cyIsInNhdmVTdXBwbGllclBheW1lbnRzIiwicGF5bWVudHMiLCJhZGRTdXBwbGllclBheW1lbnQiLCJwYXltZW50IiwibmV3UGF5bWVudCIsImFtb3VudCIsInVwZGF0ZVB1cmNoYXNlUGF5bWVudFN0YXR1cyIsImdldFN1cHBsaWVyUGF5bWVudHNCeVN1cHBsaWVyIiwic3VwcGxpZXJJZCIsImN1cnJlbnRfYmFsYW5jZSIsInBheW1lbnRBbW91bnQiLCJwYWlkX2Ftb3VudCIsInJlbWFpbmluZ19iYWxhbmNlIiwicGF5bWVudF9zdGF0dXMiLCJwcm9kdWN0SWQiLCJtb3ZlbWVudFR5cGUiLCJyZWZlcmVuY2VJZCIsInByb2R1Y3RzIiwicHJvZHVjdEluZGV4IiwicHJvZHVjdCIsInByZXZpb3VzU3RvY2siLCJzdG9jayIsIm5ld1N0b2NrIiwiYWRkU3RvY2tNb3ZlbWVudCIsInByb2R1Y3RfbmFtZSIsIm5hbWUiLCJtb3ZlbWVudF90eXBlIiwicXVhbnRpdHlfY2hhbmdlIiwicHJldmlvdXNfc3RvY2siLCJuZXdfc3RvY2siLCJyZWZlcmVuY2VfaWQiLCJyZWZlcmVuY2VfdHlwZSIsIm5vdGVzIiwiZ2V0U3RvY2tNb3ZlbWVudHMiLCJzYXZlU3RvY2tNb3ZlbWVudHMiLCJtb3ZlbWVudHMiLCJtb3ZlbWVudCIsIm5ld01vdmVtZW50IiwiZ2V0U3RvY2tNb3ZlbWVudHNCeVByb2R1Y3QiLCJnZXRDYXRlZ29yaWVzIiwic2F2ZUNhdGVnb3JpZXMiLCJjYXRlZ29yaWVzIiwiYWRkQ2F0ZWdvcnkiLCJjYXRlZ29yeSIsIm5ld0NhdGVnb3J5IiwiaW5pdGlhbGl6ZURlZmF1bHREYXRhIiwibGVuZ3RoIiwiZGVmYXVsdENhdGVnb3JpZXMiLCJkZXNjcmlwdGlvbiIsImZvckVhY2giLCJjYXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/inventory-storage.ts\n"));

/***/ })

});