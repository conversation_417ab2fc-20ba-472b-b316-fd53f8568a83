-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table
CREATE TABLE users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    full_name TEXT NOT NULL,
    gender TEXT CHECK (gender IN ('male', 'female')) NOT NULL,
    age INTEGER NOT NULL CHECK (age > 0 AND age < 120),
    phone TEXT NOT NULL UNIQUE,
    email TEXT UNIQUE,
    pregnant BOOLEAN DEFAULT FALSE,
    situation TEXT DEFAULT 'active', -- active, pregnant, sick, injured, etc.
    remarks TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sports_pricing table
CREATE TABLE sports_pricing (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    sport TEXT NOT NULL,
    gender TEXT CHECK (gender IN ('male', 'female', 'both')) NOT NULL DEFAULT 'both',
    age_group TEXT CHECK (age_group IN ('child', 'adult', 'senior', 'all')) NOT NULL DEFAULT 'all',
    monthly_price DECIMAL(10,2) NOT NULL,
    quarterly_price DECIMAL(10,2) NOT NULL,
    yearly_price DECIMAL(10,2) NOT NULL,
    pregnancy_allowed BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscriptions table
CREATE TABLE subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    sport TEXT NOT NULL,
    plan_type TEXT CHECK (plan_type IN ('monthly', 'quarterly', 'yearly')) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    price_dzd DECIMAL(10,2) NOT NULL,
    status TEXT CHECK (status IN ('active', 'expiring', 'expired')) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create products table
CREATE TABLE products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    price_dzd DECIMAL(10,2) NOT NULL,
    stock INTEGER NOT NULL DEFAULT 0,
    min_stock INTEGER DEFAULT 5, -- Minimum stock level for alerts
    expiry_date DATE,
    image_url TEXT,
    barcode TEXT UNIQUE, -- Barcode for scanning
    qr_code TEXT UNIQUE, -- QR code for scanning
    description TEXT, -- Product description
    brand TEXT, -- Product brand
    unit TEXT DEFAULT 'piece', -- Unit of measurement (piece, kg, liter, etc.)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sales table
CREATE TABLE sales (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    total_price_dzd DECIMAL(10,2) NOT NULL,
    payment_type TEXT CHECK (payment_type IN ('cash', 'card')) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sales_items table
CREATE TABLE sales_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    sale_id UUID REFERENCES sales(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price_dzd DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create classes table
CREATE TABLE classes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    sport TEXT NOT NULL,
    date_time TIMESTAMP WITH TIME ZONE NOT NULL,
    trainer_name TEXT NOT NULL,
    capacity INTEGER NOT NULL CHECK (capacity > 0),
    registered_count INTEGER DEFAULT 0 CHECK (registered_count >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create suppliers table
CREATE TABLE suppliers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    contact_person TEXT,
    phone TEXT,
    email TEXT,
    address TEXT,
    tax_number TEXT,
    payment_terms TEXT DEFAULT 'cash', -- cash, credit_30, credit_60, etc.
    credit_limit DECIMAL(10,2) DEFAULT 0,
    current_balance DECIMAL(10,2) DEFAULT 0, -- Outstanding balance for credit suppliers
    notes TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create purchases table
CREATE TABLE purchases (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    supplier_id UUID REFERENCES suppliers(id) ON DELETE RESTRICT,
    invoice_number TEXT,
    purchase_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_type TEXT CHECK (payment_type IN ('cash', 'credit')) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    remaining_balance DECIMAL(10,2) DEFAULT 0,
    payment_status TEXT CHECK (payment_status IN ('paid', 'partial', 'unpaid')) DEFAULT 'unpaid',
    due_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create purchase_items table
CREATE TABLE purchase_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    purchase_id UUID REFERENCES purchases(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE RESTRICT,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    expiry_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create supplier_payments table for tracking payments to suppliers
CREATE TABLE supplier_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    supplier_id UUID REFERENCES suppliers(id) ON DELETE RESTRICT,
    purchase_id UUID REFERENCES purchases(id) ON DELETE SET NULL,
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    amount DECIMAL(10,2) NOT NULL,
    payment_method TEXT CHECK (payment_method IN ('cash', 'bank_transfer', 'check', 'card')) NOT NULL,
    reference_number TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create stock_movements table for tracking all stock changes
CREATE TABLE stock_movements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    movement_type TEXT CHECK (movement_type IN ('purchase', 'sale', 'adjustment', 'return', 'expired')) NOT NULL,
    quantity_change INTEGER NOT NULL, -- Positive for additions, negative for reductions
    previous_stock INTEGER NOT NULL,
    new_stock INTEGER NOT NULL,
    reference_id UUID, -- Can reference purchase_id, sale_id, etc.
    reference_type TEXT, -- 'purchase', 'sale', 'manual_adjustment', etc.
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create categories table for better product organization
CREATE TABLE categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    parent_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_end_date ON subscriptions(end_date);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_stock ON products(stock);
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_products_qr_code ON products(qr_code);
CREATE INDEX idx_sales_created_at ON sales(created_at);
CREATE INDEX idx_sales_items_sale_id ON sales_items(sale_id);
CREATE INDEX idx_sales_items_product_id ON sales_items(product_id);
CREATE INDEX idx_classes_date_time ON classes(date_time);
CREATE INDEX idx_classes_sport ON classes(sport);

-- Indexes for new inventory tables
CREATE INDEX idx_suppliers_name ON suppliers(name);
CREATE INDEX idx_suppliers_active ON suppliers(active);
CREATE INDEX idx_purchases_supplier_id ON purchases(supplier_id);
CREATE INDEX idx_purchases_purchase_date ON purchases(purchase_date);
CREATE INDEX idx_purchases_payment_status ON purchases(payment_status);
CREATE INDEX idx_purchase_items_purchase_id ON purchase_items(purchase_id);
CREATE INDEX idx_purchase_items_product_id ON purchase_items(product_id);
CREATE INDEX idx_supplier_payments_supplier_id ON supplier_payments(supplier_id);
CREATE INDEX idx_supplier_payments_purchase_id ON supplier_payments(purchase_id);
CREATE INDEX idx_supplier_payments_payment_date ON supplier_payments(payment_date);
CREATE INDEX idx_stock_movements_product_id ON stock_movements(product_id);
CREATE INDEX idx_stock_movements_movement_type ON stock_movements(movement_type);
CREATE INDEX idx_stock_movements_created_at ON stock_movements(created_at);
CREATE INDEX idx_categories_name ON categories(name);
CREATE INDEX idx_categories_parent_id ON categories(parent_id);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sports_pricing_updated_at BEFORE UPDATE ON sports_pricing
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_classes_updated_at BEFORE UPDATE ON classes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Triggers for new inventory tables
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_purchases_updated_at BEFORE UPDATE ON purchases
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update subscription status based on end_date
CREATE OR REPLACE FUNCTION update_subscription_status()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.end_date < CURRENT_DATE THEN
        NEW.status = 'expired';
    ELSIF NEW.end_date <= CURRENT_DATE + INTERVAL '7 days' THEN
        NEW.status = 'expiring';
    ELSE
        NEW.status = 'active';
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for automatic subscription status updates
CREATE TRIGGER update_subscription_status_trigger BEFORE INSERT OR UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_subscription_status();

-- Function to update product stock after sale
CREATE OR REPLACE FUNCTION update_product_stock()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE products
    SET stock = stock - NEW.quantity
    WHERE id = NEW.product_id;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for automatic stock updates
CREATE TRIGGER update_product_stock_trigger AFTER INSERT ON sales_items
    FOR EACH ROW EXECUTE FUNCTION update_product_stock();

-- Function to update product stock after purchase
CREATE OR REPLACE FUNCTION update_product_stock_purchase()
RETURNS TRIGGER AS $$
BEGIN
    -- Update product stock
    UPDATE products
    SET stock = stock + NEW.quantity
    WHERE id = NEW.product_id;

    -- Create stock movement record
    INSERT INTO stock_movements (
        product_id,
        movement_type,
        quantity_change,
        previous_stock,
        new_stock,
        reference_id,
        reference_type,
        notes
    )
    SELECT
        NEW.product_id,
        'purchase',
        NEW.quantity,
        p.stock - NEW.quantity,
        p.stock,
        NEW.purchase_id,
        'purchase',
        'Stock added from purchase'
    FROM products p
    WHERE p.id = NEW.product_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for automatic stock updates on purchase
CREATE TRIGGER update_product_stock_purchase_trigger AFTER INSERT ON purchase_items
    FOR EACH ROW EXECUTE FUNCTION update_product_stock_purchase();

-- Function to update supplier balance after purchase
CREATE OR REPLACE FUNCTION update_supplier_balance_purchase()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.payment_type = 'credit' THEN
        UPDATE suppliers
        SET current_balance = current_balance + NEW.total_amount
        WHERE id = NEW.supplier_id;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for supplier balance updates
CREATE TRIGGER update_supplier_balance_purchase_trigger AFTER INSERT ON purchases
    FOR EACH ROW EXECUTE FUNCTION update_supplier_balance_purchase();

-- Function to update supplier balance and purchase status after payment
CREATE OR REPLACE FUNCTION update_supplier_balance_payment()
RETURNS TRIGGER AS $$
BEGIN
    -- Update supplier balance
    UPDATE suppliers
    SET current_balance = current_balance - NEW.amount
    WHERE id = NEW.supplier_id;

    -- Update purchase payment status if purchase_id is provided
    IF NEW.purchase_id IS NOT NULL THEN
        UPDATE purchases
        SET
            paid_amount = paid_amount + NEW.amount,
            remaining_balance = total_amount - (paid_amount + NEW.amount),
            payment_status = CASE
                WHEN (paid_amount + NEW.amount) >= total_amount THEN 'paid'
                WHEN (paid_amount + NEW.amount) > 0 THEN 'partial'
                ELSE 'unpaid'
            END
        WHERE id = NEW.purchase_id;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for payment updates
CREATE TRIGGER update_supplier_balance_payment_trigger AFTER INSERT ON supplier_payments
    FOR EACH ROW EXECUTE FUNCTION update_supplier_balance_payment();

-- Function to create stock movement for sales
CREATE OR REPLACE FUNCTION create_stock_movement_sale()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO stock_movements (
        product_id,
        movement_type,
        quantity_change,
        previous_stock,
        new_stock,
        reference_id,
        reference_type,
        notes
    )
    SELECT
        NEW.product_id,
        'sale',
        -NEW.quantity,
        p.stock + NEW.quantity,
        p.stock,
        NEW.sale_id,
        'sale',
        'Stock reduced from sale'
    FROM products p
    WHERE p.id = NEW.product_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for stock movement on sales
CREATE TRIGGER create_stock_movement_sale_trigger AFTER INSERT ON sales_items
    FOR EACH ROW EXECUTE FUNCTION create_stock_movement_sale();

-- Insert default categories
INSERT INTO categories (name, description) VALUES
('Supplements', 'Protein powders, vitamins, and nutritional supplements'),
('Beverages', 'Energy drinks, water, sports drinks'),
('Equipment', 'Gym equipment, accessories, and gear'),
('Apparel', 'Gym clothing, shoes, and accessories'),
('Snacks', 'Protein bars, healthy snacks'),
('Personal Care', 'Towels, toiletries, hygiene products'),
('Accessories', 'Gloves, belts, straps, and other accessories'),
('Recovery', 'Recovery tools, massage equipment');

-- Function to check low stock and send alerts
CREATE OR REPLACE FUNCTION check_low_stock()
RETURNS TABLE(
    product_id UUID,
    product_name TEXT,
    current_stock INTEGER,
    min_stock INTEGER,
    category TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        p.stock,
        p.min_stock,
        p.category
    FROM products p
    WHERE p.stock <= p.min_stock
    AND p.stock > 0
    ORDER BY (p.stock::FLOAT / p.min_stock::FLOAT) ASC;
END;
$$ language 'plpgsql';

-- Function to get products expiring soon
CREATE OR REPLACE FUNCTION check_expiring_products(days_ahead INTEGER DEFAULT 30)
RETURNS TABLE(
    product_id UUID,
    product_name TEXT,
    expiry_date DATE,
    days_until_expiry INTEGER,
    current_stock INTEGER,
    category TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        p.expiry_date,
        (p.expiry_date - CURRENT_DATE)::INTEGER,
        p.stock,
        p.category
    FROM products p
    WHERE p.expiry_date IS NOT NULL
    AND p.expiry_date <= CURRENT_DATE + INTERVAL '1 day' * days_ahead
    AND p.stock > 0
    ORDER BY p.expiry_date ASC;
END;
$$ language 'plpgsql';
