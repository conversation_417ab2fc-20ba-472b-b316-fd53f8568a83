'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/components/providers'
import { ExtendedProduct, InventoryStorage, StockMovement } from '@/lib/inventory-storage'
import { formatCurrency } from '@/lib/utils'
import {
  X,
  Package,
  DollarSign,
  Calendar,
  Hash,
  Tag,
  FileText,
  Building,
  Ruler,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Image as ImageIcon,
  Edit,
} from 'lucide-react'

interface ProductDetailsModalProps {
  product: ExtendedProduct
  onClose: () => void
  onEdit: () => void
}

export function ProductDetailsModal({ product, onClose, onEdit }: ProductDetailsModalProps) {
  const { t } = useLanguage()
  const [stockMovements, setStockMovements] = useState<StockMovement[]>([])

  useEffect(() => {
    // Load stock movements for this product
    const movements = InventoryStorage.getStockMovementsByProduct(product.id)
    setStockMovements(movements.slice(0, 10)) // Show last 10 movements
  }, [product.id])

  const getStockStatus = () => {
    if (product.stock === 0) return { status: 'out', color: 'text-red-500', bg: 'bg-red-50', text: 'Out of Stock' }
    if (product.stock <= product.min_stock) return { status: 'low', color: 'text-orange-500', bg: 'bg-orange-50', text: 'Low Stock' }
    return { status: 'good', color: 'text-green-500', bg: 'bg-green-50', text: 'In Stock' }
  }

  const getExpiryStatus = () => {
    if (!product.expiry_date) return null
    
    const today = new Date()
    const expiry = new Date(product.expiry_date)
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysUntilExpiry < 0) return { status: 'expired', color: 'text-red-500', text: 'Expired', bg: 'bg-red-50' }
    if (daysUntilExpiry <= 7) return { status: 'expiring', color: 'text-orange-500', text: `Expires in ${daysUntilExpiry} days`, bg: 'bg-orange-50' }
    if (daysUntilExpiry <= 30) return { status: 'warning', color: 'text-yellow-500', text: `Expires in ${daysUntilExpiry} days`, bg: 'bg-yellow-50' }
    return { status: 'good', color: 'text-green-500', text: `Expires in ${daysUntilExpiry} days`, bg: 'bg-green-50' }
  }

  const stockStatus = getStockStatus()
  const expiryStatus = getExpiryStatus()

  const getMovementIcon = (type: StockMovement['movement_type']) => {
    switch (type) {
      case 'purchase':
        return <TrendingUp className="w-4 h-4 text-green-500" />
      case 'sale':
        return <TrendingDown className="w-4 h-4 text-blue-500" />
      case 'adjustment':
        return <Edit className="w-4 h-4 text-orange-500" />
      default:
        return <Package className="w-4 h-4 text-gray-500" />
    }
  }

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          // Don't close modal when clicking outside
        }
      }}
    >
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto glass border-white/20">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Package className="w-6 h-6" />
            <span>Product Details</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={onEdit}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Product Header */}
          <div className="flex items-start space-x-6">
            <div className="flex-shrink-0">
              {product.image_url ? (
                <img 
                  src={product.image_url} 
                  alt={product.name}
                  className="w-32 h-32 rounded-lg object-cover"
                />
              ) : (
                <div className="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <ImageIcon className="w-16 h-16 text-gray-400" />
                </div>
              )}
            </div>
            
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{product.name}</h2>
              {product.brand && (
                <p className="text-lg text-gray-600 dark:text-gray-300 mt-1">{product.brand}</p>
              )}
              
              <div className="flex items-center space-x-4 mt-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${stockStatus.bg} ${stockStatus.color}`}>
                  {stockStatus.text}
                </span>
                
                {expiryStatus && (
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${expiryStatus.bg} ${expiryStatus.color}`}>
                    {expiryStatus.text}
                  </span>
                )}
              </div>

              {product.description && (
                <p className="text-gray-600 dark:text-gray-300 mt-4">{product.description}</p>
              )}
            </div>
          </div>

          {/* Product Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Tag className="w-5 h-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Category</p>
                    <p className="font-medium text-gray-900 dark:text-white">{product.category}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-5 h-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Price</p>
                    <p className="font-medium text-gray-900 dark:text-white">{formatCurrency(product.price_dzd)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Package className="w-5 h-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Stock</p>
                    <p className="font-medium text-gray-900 dark:text-white">{product.stock} {product.unit}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="w-5 h-5 text-orange-500" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Min Stock</p>
                    <p className="font-medium text-gray-900 dark:text-white">{product.min_stock} {product.unit}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Ruler className="w-5 h-5 text-indigo-500" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Unit</p>
                    <p className="font-medium text-gray-900 dark:text-white">{product.unit}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-5 h-5 text-teal-500" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Total Value</p>
                    <p className="font-medium text-gray-900 dark:text-white">{formatCurrency(product.stock * product.price_dzd)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="glass border-white/20">
              <CardHeader>
                <CardTitle className="text-lg">Product Codes</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {product.barcode && (
                  <div className="flex items-center space-x-2">
                    <Hash className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Barcode</p>
                      <p className="font-mono text-gray-900 dark:text-white">{product.barcode}</p>
                    </div>
                  </div>
                )}
                
                {product.qr_code && (
                  <div className="flex items-center space-x-2">
                    <Hash className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">QR Code</p>
                      <p className="font-mono text-gray-900 dark:text-white">{product.qr_code}</p>
                    </div>
                  </div>
                )}

                {!product.barcode && !product.qr_code && (
                  <p className="text-gray-500 dark:text-gray-400 text-sm">No product codes assigned</p>
                )}
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardHeader>
                <CardTitle className="text-lg">Dates</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Created</p>
                    <p className="text-gray-900 dark:text-white">{new Date(product.created_at).toLocaleDateString()}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Last Updated</p>
                    <p className="text-gray-900 dark:text-white">{new Date(product.updated_at).toLocaleDateString()}</p>
                  </div>
                </div>

                {product.expiry_date && (
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Expiry Date</p>
                      <p className="text-gray-900 dark:text-white">{new Date(product.expiry_date).toLocaleDateString()}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Stock Movements */}
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle className="text-lg">Recent Stock Movements</CardTitle>
            </CardHeader>
            <CardContent>
              {stockMovements.length > 0 ? (
                <div className="space-y-3">
                  {stockMovements.map((movement) => (
                    <div key={movement.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getMovementIcon(movement.movement_type)}
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white capitalize">
                            {movement.movement_type}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {new Date(movement.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-medium ${movement.quantity_change > 0 ? 'text-green-500' : 'text-red-500'}`}>
                          {movement.quantity_change > 0 ? '+' : ''}{movement.quantity_change}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Stock: {movement.new_stock}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  No stock movements recorded
                </p>
              )}
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}
