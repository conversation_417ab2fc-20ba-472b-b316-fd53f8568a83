'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/components/providers'
import { InventoryStorage, ExtendedProduct, StockMovement, Purchase, Supplier } from '@/lib/inventory-storage'
import { formatCurrency } from '@/lib/utils'
import {
  X,
  Download,
  FileText,
  TrendingUp,
  TrendingDown,
  Package,
  AlertTriangle,
  Calendar,
  DollarSign,
  BarChart3,
} from 'lucide-react'

interface InventoryReportsProps {
  onClose: () => void
}

export function InventoryReports({ onClose }: InventoryReportsProps) {
  const { t } = useLanguage()

  const [products, setProducts] = useState<ExtendedProduct[]>([])
  const [stockMovements, setStockMovements] = useState<StockMovement[]>([])
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [selectedReport, setSelectedReport] = useState<string>('overview')

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    setProducts(InventoryStorage.getFromStorage<ExtendedProduct>('gym_products'))
    setStockMovements(InventoryStorage.getStockMovements())
    setPurchases(InventoryStorage.getPurchases())
    setSuppliers(InventoryStorage.getSuppliers())
  }

  // Calculate various metrics
  const calculateMetrics = () => {
    const totalProducts = products.length
    const totalValue = products.reduce((sum, p) => sum + (p.stock * p.price_dzd), 0)
    const lowStockProducts = products.filter(p => p.stock <= p.min_stock)
    const outOfStockProducts = products.filter(p => p.stock === 0)
    
    const today = new Date()
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
    
    const recentMovements = stockMovements.filter(m => 
      new Date(m.created_at) >= thirtyDaysAgo
    )
    
    const recentPurchases = purchases.filter(p => 
      new Date(p.created_at) >= thirtyDaysAgo
    )
    
    const totalPurchaseValue = recentPurchases.reduce((sum, p) => sum + p.total_amount, 0)
    
    const expiringProducts = products.filter(p => {
      if (!p.expiry_date) return false
      const expiryDate = new Date(p.expiry_date)
      const thirtyDaysFromNow = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)
      return expiryDate <= thirtyDaysFromNow && expiryDate >= today
    })

    const expiredProducts = products.filter(p => {
      if (!p.expiry_date) return false
      const expiryDate = new Date(p.expiry_date)
      return expiryDate < today
    })

    return {
      totalProducts,
      totalValue,
      lowStockProducts,
      outOfStockProducts,
      recentMovements,
      recentPurchases,
      totalPurchaseValue,
      expiringProducts,
      expiredProducts,
    }
  }

  const metrics = calculateMetrics()

  const exportToCSV = (data: any[], filename: string, headers: string[]) => {
    const csvData = [
      headers.join(','),
      ...data.map(row => headers.map(header => {
        const value = row[header.toLowerCase().replace(/\s+/g, '_')]
        return typeof value === 'string' ? `"${value}"` : value
      }).join(','))
    ].join('\n')

    const blob = new Blob([csvData], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const exportLowStockReport = () => {
    const data = metrics.lowStockProducts.map(p => ({
      product_name: p.name,
      category: p.category,
      current_stock: p.stock,
      minimum_stock: p.min_stock,
      unit: p.unit,
      value: p.stock * p.price_dzd,
      status: p.stock === 0 ? 'Out of Stock' : 'Low Stock'
    }))
    
    exportToCSV(data, 'low-stock-report', [
      'Product Name', 'Category', 'Current Stock', 'Minimum Stock', 'Unit', 'Value', 'Status'
    ])
  }

  const exportInventoryValuation = () => {
    const data = products.map(p => ({
      product_name: p.name,
      category: p.category,
      stock: p.stock,
      unit_price: p.price_dzd,
      total_value: p.stock * p.price_dzd,
      unit: p.unit
    }))
    
    exportToCSV(data, 'inventory-valuation', [
      'Product Name', 'Category', 'Stock', 'Unit Price', 'Total Value', 'Unit'
    ])
  }

  const exportExpiryReport = () => {
    const data = [...metrics.expiringProducts, ...metrics.expiredProducts].map(p => ({
      product_name: p.name,
      category: p.category,
      stock: p.stock,
      expiry_date: p.expiry_date,
      days_until_expiry: p.expiry_date ? Math.ceil((new Date(p.expiry_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 'N/A',
      status: p.expiry_date && new Date(p.expiry_date) < new Date() ? 'Expired' : 'Expiring Soon'
    }))
    
    exportToCSV(data, 'expiry-report', [
      'Product Name', 'Category', 'Stock', 'Expiry Date', 'Days Until Expiry', 'Status'
    ])
  }

  const reportTypes = [
    { id: 'overview', name: 'Overview', icon: BarChart3 },
    { id: 'low_stock', name: 'Low Stock', icon: AlertTriangle },
    { id: 'expiry', name: 'Expiry Report', icon: Calendar },
    { id: 'valuation', name: 'Inventory Valuation', icon: DollarSign },
    { id: 'movements', name: 'Stock Movements', icon: TrendingUp },
  ]

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-y-auto glass border-white/20">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-6 h-6" />
            <span>Inventory Reports</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Report Type Selection */}
          <div className="flex flex-wrap gap-2">
            {reportTypes.map(report => {
              const Icon = report.icon
              return (
                <Button
                  key={report.id}
                  variant={selectedReport === report.id ? 'default' : 'outline'}
                  onClick={() => setSelectedReport(report.id)}
                  className="flex items-center space-x-2"
                >
                  <Icon className="w-4 h-4" />
                  <span>{report.name}</span>
                </Button>
              )
            })}
          </div>

          {/* Overview Report */}
          {selectedReport === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="glass border-white/20">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Package className="w-8 h-8 text-blue-500" />
                      <div>
                        <p className="text-sm text-gray-600">Total Products</p>
                        <p className="text-2xl font-bold">{metrics.totalProducts}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass border-white/20">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-8 h-8 text-green-500" />
                      <div>
                        <p className="text-sm text-gray-600">Total Value</p>
                        <p className="text-2xl font-bold">{formatCurrency(metrics.totalValue)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass border-white/20">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="w-8 h-8 text-orange-500" />
                      <div>
                        <p className="text-sm text-gray-600">Low Stock Items</p>
                        <p className="text-2xl font-bold">{metrics.lowStockProducts.length}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass border-white/20">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-8 h-8 text-red-500" />
                      <div>
                        <p className="text-sm text-gray-600">Expiring Soon</p>
                        <p className="text-2xl font-bold">{metrics.expiringProducts.length}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="glass border-white/20">
                  <CardHeader>
                    <CardTitle>Recent Activity (30 days)</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span>Stock Movements:</span>
                        <span className="font-medium">{metrics.recentMovements.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Purchases:</span>
                        <span className="font-medium">{metrics.recentPurchases.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Purchase Value:</span>
                        <span className="font-medium">{formatCurrency(metrics.totalPurchaseValue)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass border-white/20">
                  <CardHeader>
                    <CardTitle>Alerts</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-red-600">Out of Stock:</span>
                        <span className="font-medium">{metrics.outOfStockProducts.length}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-orange-600">Low Stock:</span>
                        <span className="font-medium">{metrics.lowStockProducts.length - metrics.outOfStockProducts.length}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-red-600">Expired:</span>
                        <span className="font-medium">{metrics.expiredProducts.length}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-yellow-600">Expiring Soon:</span>
                        <span className="font-medium">{metrics.expiringProducts.length}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Low Stock Report */}
          {selectedReport === 'low_stock' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Low Stock Products</h3>
                <Button onClick={exportLowStockReport} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Export CSV
                </Button>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4">Product</th>
                      <th className="text-left py-3 px-4">Category</th>
                      <th className="text-left py-3 px-4">Current Stock</th>
                      <th className="text-left py-3 px-4">Min Stock</th>
                      <th className="text-left py-3 px-4">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {metrics.lowStockProducts.map(product => (
                      <tr key={product.id} className="border-b border-gray-100">
                        <td className="py-3 px-4 font-medium">{product.name}</td>
                        <td className="py-3 px-4">{product.category}</td>
                        <td className="py-3 px-4">{product.stock} {product.unit}</td>
                        <td className="py-3 px-4">{product.min_stock} {product.unit}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-sm ${
                            product.stock === 0 
                              ? 'bg-red-100 text-red-800' 
                              : 'bg-orange-100 text-orange-800'
                          }`}>
                            {product.stock === 0 ? 'Out of Stock' : 'Low Stock'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Expiry Report */}
          {selectedReport === 'expiry' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Product Expiry Report</h3>
                <Button onClick={exportExpiryReport} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Export CSV
                </Button>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4">Product</th>
                      <th className="text-left py-3 px-4">Category</th>
                      <th className="text-left py-3 px-4">Stock</th>
                      <th className="text-left py-3 px-4">Expiry Date</th>
                      <th className="text-left py-3 px-4">Days Left</th>
                      <th className="text-left py-3 px-4">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[...metrics.expiredProducts, ...metrics.expiringProducts].map(product => {
                      const daysLeft = product.expiry_date 
                        ? Math.ceil((new Date(product.expiry_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
                        : 0
                      const isExpired = daysLeft < 0
                      
                      return (
                        <tr key={product.id} className="border-b border-gray-100">
                          <td className="py-3 px-4 font-medium">{product.name}</td>
                          <td className="py-3 px-4">{product.category}</td>
                          <td className="py-3 px-4">{product.stock} {product.unit}</td>
                          <td className="py-3 px-4">
                            {product.expiry_date ? new Date(product.expiry_date).toLocaleDateString() : 'N/A'}
                          </td>
                          <td className="py-3 px-4">
                            {isExpired ? `${Math.abs(daysLeft)} days ago` : `${daysLeft} days`}
                          </td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded-full text-sm ${
                              isExpired 
                                ? 'bg-red-100 text-red-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {isExpired ? 'Expired' : 'Expiring Soon'}
                            </span>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Inventory Valuation */}
          {selectedReport === 'valuation' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Inventory Valuation</h3>
                <Button onClick={exportInventoryValuation} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Export CSV
                </Button>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4">Product</th>
                      <th className="text-left py-3 px-4">Category</th>
                      <th className="text-left py-3 px-4">Stock</th>
                      <th className="text-left py-3 px-4">Unit Price</th>
                      <th className="text-left py-3 px-4">Total Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map(product => (
                      <tr key={product.id} className="border-b border-gray-100">
                        <td className="py-3 px-4 font-medium">{product.name}</td>
                        <td className="py-3 px-4">{product.category}</td>
                        <td className="py-3 px-4">{product.stock} {product.unit}</td>
                        <td className="py-3 px-4">{formatCurrency(product.price_dzd)}</td>
                        <td className="py-3 px-4 font-medium">
                          {formatCurrency(product.stock * product.price_dzd)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t-2 border-gray-300 font-bold">
                      <td colSpan={4} className="py-3 px-4 text-right">Total Inventory Value:</td>
                      <td className="py-3 px-4">{formatCurrency(metrics.totalValue)}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
