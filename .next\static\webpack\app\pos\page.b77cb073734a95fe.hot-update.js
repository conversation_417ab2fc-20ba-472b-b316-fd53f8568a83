"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Try to load from Supabase first\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n            if (error) throw error;\n            setProducts(data || []);\n        } catch (error) {\n            // Fallback to localStorage for development\n            try {\n                const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n                if (storedProducts.length > 0) {\n                    const productData = storedProducts.filter((p)=>p.stock > 0);\n                    setProducts(productData.map((p)=>({\n                            id: p.id,\n                            name: p.name,\n                            category: p.category,\n                            price_dzd: p.price_dzd,\n                            stock: p.stock,\n                            image_url: p.image_url,\n                            barcode: p.barcode,\n                            qr_code: p.qr_code,\n                            expiry_date: p.expiry_date\n                        })));\n                } else {\n                    // Create sample products for development\n                    const sampleProducts = [\n                        {\n                            id: \"1\",\n                            name: \"Protein Powder\",\n                            category: \"Supplements\",\n                            price_dzd: 5000,\n                            stock: 25,\n                            barcode: \"1234567890123\",\n                            expiry_date: \"2024-12-31\"\n                        },\n                        {\n                            id: \"2\",\n                            name: \"Energy Drink\",\n                            category: \"Beverages\",\n                            price_dzd: 200,\n                            stock: 50,\n                            barcode: \"2345678901234\",\n                            expiry_date: \"2024-06-30\"\n                        },\n                        {\n                            id: \"3\",\n                            name: \"Gym Towel\",\n                            category: \"Accessories\",\n                            price_dzd: 800,\n                            stock: 15,\n                            barcode: \"3456789012345\"\n                        }\n                    ];\n                    setProducts(sampleProducts);\n                    localStorage.setItem(\"gym_products\", JSON.stringify(sampleProducts));\n                }\n            } catch (localError) {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to load products\",\n                    variant: \"destructive\"\n                });\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Update product stock in localStorage\n            const updatedProducts = products.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity\n                    };\n                }\n                return product;\n            });\n            localStorage.setItem(\"gym_products\", JSON.stringify(updatedProducts));\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 392,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 391,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 overflow-auto max-h-[calc(100vh-16rem)]\",\n                                children: filteredProducts.map((product)=>{\n                                    const expired = isProductExpired(product);\n                                    const expiringSoon = isProductExpiringSoon(product);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\"),\n                                        onClick: ()=>!expired && addToCart(product),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-gray-900 dark:text-white line-clamp-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    t(\"expiry_date\"),\n                                                                    \": \",\n                                                                    new Date(product.expiry_date).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-red-600 dark:text-red-400\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            t(\"stock\"),\n                                                                            \": \",\n                                                                            product.stock\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 507,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 576,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 674,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 673,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 738,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 746,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 400,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/inventory-storage.ts":
/*!**************************************!*\
  !*** ./src/lib/inventory-storage.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InventoryStorage: function() { return /* binding */ InventoryStorage; }\n/* harmony export */ });\n// localStorage-based inventory management system\n// localStorage keys\nconst STORAGE_KEYS = {\n    SUPPLIERS: \"gym_suppliers\",\n    PURCHASES: \"gym_purchases\",\n    PURCHASE_ITEMS: \"gym_purchase_items\",\n    SUPPLIER_PAYMENTS: \"gym_supplier_payments\",\n    STOCK_MOVEMENTS: \"gym_stock_movements\",\n    CATEGORIES: \"gym_categories\",\n    PRODUCTS: \"gym_products\"\n};\n// Utility functions for localStorage operations\nclass InventoryStorage {\n    // Generic storage operations\n    static getFromStorage(key) {\n        try {\n            const data = localStorage.getItem(key);\n            return data ? JSON.parse(data) : [];\n        } catch (error) {\n            console.error(\"Error reading \".concat(key, \" from localStorage:\"), error);\n            return [];\n        }\n    }\n    static saveToStorage(key, data) {\n        try {\n            localStorage.setItem(key, JSON.stringify(data));\n        } catch (error) {\n            console.error(\"Error saving \".concat(key, \" to localStorage:\"), error);\n        }\n    }\n    static generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    // Supplier operations\n    static getSuppliers() {\n        return this.getFromStorage(STORAGE_KEYS.SUPPLIERS);\n    }\n    static saveSuppliers(suppliers) {\n        this.saveToStorage(STORAGE_KEYS.SUPPLIERS, suppliers);\n    }\n    static addSupplier(supplier) {\n        const suppliers = this.getSuppliers();\n        const newSupplier = {\n            ...supplier,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        suppliers.push(newSupplier);\n        this.saveSuppliers(suppliers);\n        return newSupplier;\n    }\n    static updateSupplier(id, updates) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === id);\n        if (index === -1) return null;\n        suppliers[index] = {\n            ...suppliers[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        this.saveSuppliers(suppliers);\n        return suppliers[index];\n    }\n    static deleteSupplier(id) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === id);\n        if (index === -1) return false;\n        suppliers[index].active = false;\n        this.saveSuppliers(suppliers);\n        return true;\n    }\n    static getSupplierById(id) {\n        const suppliers = this.getSuppliers();\n        return suppliers.find((s)=>s.id === id) || null;\n    }\n    // Purchase operations\n    static getPurchases() {\n        return this.getFromStorage(STORAGE_KEYS.PURCHASES);\n    }\n    static savePurchases(purchases) {\n        this.saveToStorage(STORAGE_KEYS.PURCHASES, purchases);\n    }\n    static addPurchase(purchase) {\n        const purchases = this.getPurchases();\n        const newPurchase = {\n            ...purchase,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        purchases.push(newPurchase);\n        this.savePurchases(purchases);\n        // Update supplier balance if credit purchase\n        if (purchase.payment_type === \"credit\") {\n            this.updateSupplierBalance(purchase.supplier_id, purchase.total_amount);\n        }\n        return newPurchase;\n    }\n    static updatePurchase(id, updates) {\n        const purchases = this.getPurchases();\n        const index = purchases.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        purchases[index] = {\n            ...purchases[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        this.savePurchases(purchases);\n        return purchases[index];\n    }\n    // Purchase Items operations\n    static getPurchaseItems() {\n        return this.getFromStorage(STORAGE_KEYS.PURCHASE_ITEMS);\n    }\n    static savePurchaseItems(items) {\n        this.saveToStorage(STORAGE_KEYS.PURCHASE_ITEMS, items);\n    }\n    static addPurchaseItem(item) {\n        const items = this.getPurchaseItems();\n        const newItem = {\n            ...item,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        items.push(newItem);\n        this.savePurchaseItems(items);\n        // Update product stock\n        this.updateProductStock(item.product_id, item.quantity, \"purchase\", newItem.id);\n        return newItem;\n    }\n    static getPurchaseItemsByPurchaseId(purchaseId) {\n        const items = this.getPurchaseItems();\n        return items.filter((item)=>item.purchase_id === purchaseId);\n    }\n    // Supplier Payment operations\n    static getSupplierPayments() {\n        return this.getFromStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS);\n    }\n    static saveSupplierPayments(payments) {\n        this.saveToStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS, payments);\n    }\n    static addSupplierPayment(payment) {\n        const payments = this.getSupplierPayments();\n        const newPayment = {\n            ...payment,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        payments.push(newPayment);\n        this.saveSupplierPayments(payments);\n        // Update supplier balance\n        this.updateSupplierBalance(payment.supplier_id, -payment.amount);\n        // Update purchase payment status if purchase_id provided\n        if (payment.purchase_id) {\n            this.updatePurchasePaymentStatus(payment.purchase_id, payment.amount);\n        }\n        return newPayment;\n    }\n    static getSupplierPaymentsBySupplier(supplierId) {\n        const payments = this.getSupplierPayments();\n        return payments.filter((payment)=>payment.supplier_id === supplierId);\n    }\n    // Helper methods\n    static updateSupplierBalance(supplierId, amount) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === supplierId);\n        if (index !== -1) {\n            suppliers[index].current_balance += amount;\n            suppliers[index].updated_at = new Date().toISOString();\n            this.saveSuppliers(suppliers);\n        }\n    }\n    static updatePurchasePaymentStatus(purchaseId, paymentAmount) {\n        const purchases = this.getPurchases();\n        const index = purchases.findIndex((p)=>p.id === purchaseId);\n        if (index !== -1) {\n            const purchase = purchases[index];\n            purchase.paid_amount += paymentAmount;\n            purchase.remaining_balance = purchase.total_amount - purchase.paid_amount;\n            if (purchase.remaining_balance <= 0) {\n                purchase.payment_status = \"paid\";\n            } else if (purchase.paid_amount > 0) {\n                purchase.payment_status = \"partial\";\n            }\n            purchase.updated_at = new Date().toISOString();\n            this.savePurchases(purchases);\n        }\n    }\n    static updateProductStock(productId, quantity, movementType, referenceId) {\n        // Get current products (assuming they're stored in localStorage)\n        const products = this.getFromStorage(STORAGE_KEYS.PRODUCTS);\n        const productIndex = products.findIndex((p)=>p.id === productId);\n        if (productIndex !== -1) {\n            const product = products[productIndex];\n            const previousStock = product.stock;\n            const newStock = previousStock + quantity;\n            // Update product stock\n            product.stock = newStock;\n            product.updated_at = new Date().toISOString();\n            this.saveToStorage(STORAGE_KEYS.PRODUCTS, products);\n            // Create stock movement record\n            this.addStockMovement({\n                product_id: productId,\n                product_name: product.name,\n                movement_type: movementType,\n                quantity_change: quantity,\n                previous_stock: previousStock,\n                new_stock: newStock,\n                reference_id: referenceId,\n                reference_type: movementType,\n                notes: \"Stock \".concat(movementType, \" - \").concat(quantity, \" units\")\n            });\n        }\n    }\n    // Stock Movement operations\n    static getStockMovements() {\n        return this.getFromStorage(STORAGE_KEYS.STOCK_MOVEMENTS);\n    }\n    static saveStockMovements(movements) {\n        this.saveToStorage(STORAGE_KEYS.STOCK_MOVEMENTS, movements);\n    }\n    static addStockMovement(movement) {\n        const movements = this.getStockMovements();\n        const newMovement = {\n            ...movement,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        movements.push(newMovement);\n        this.saveStockMovements(movements);\n        return newMovement;\n    }\n    static getStockMovementsByProduct(productId) {\n        const movements = this.getStockMovements();\n        return movements.filter((movement)=>movement.product_id === productId);\n    }\n    // Category operations\n    static getCategories() {\n        return this.getFromStorage(STORAGE_KEYS.CATEGORIES);\n    }\n    static saveCategories(categories) {\n        this.saveToStorage(STORAGE_KEYS.CATEGORIES, categories);\n    }\n    static addCategory(category) {\n        const categories = this.getCategories();\n        const newCategory = {\n            ...category,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        categories.push(newCategory);\n        this.saveCategories(categories);\n        return newCategory;\n    }\n    // Initialize default data\n    static initializeDefaultData() {\n        // Initialize default categories if none exist\n        const categories = this.getCategories();\n        if (categories.length === 0) {\n            const defaultCategories = [\n                {\n                    name: \"Supplements\",\n                    description: \"Protein powders, vitamins, and nutritional supplements\"\n                },\n                {\n                    name: \"Beverages\",\n                    description: \"Energy drinks, water, sports drinks\"\n                },\n                {\n                    name: \"Equipment\",\n                    description: \"Gym equipment, accessories, and gear\"\n                },\n                {\n                    name: \"Apparel\",\n                    description: \"Gym clothing, shoes, and accessories\"\n                },\n                {\n                    name: \"Snacks\",\n                    description: \"Protein bars, healthy snacks\"\n                },\n                {\n                    name: \"Personal Care\",\n                    description: \"Towels, toiletries, hygiene products\"\n                },\n                {\n                    name: \"Accessories\",\n                    description: \"Gloves, belts, straps, and other accessories\"\n                },\n                {\n                    name: \"Recovery\",\n                    description: \"Recovery tools, massage equipment\"\n                }\n            ];\n            defaultCategories.forEach((cat)=>{\n                this.addCategory({\n                    ...cat,\n                    active: true\n                });\n            });\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/inventory-storage.ts\n"));

/***/ })

});