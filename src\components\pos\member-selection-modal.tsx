'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import {
  X,
  Search,
  User,
  Phone,
  Mail,
  Users,
} from 'lucide-react'

interface Member {
  id: string
  full_name: string
  phone: string
  email?: string
  gender: 'male' | 'female'
  age: number
  situation: string
}

interface MemberSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  onSelectMember: (member: Member) => void
}

export function MemberSelectionModal({ 
  isOpen, 
  onClose, 
  onSelectMember 
}: MemberSelectionModalProps) {
  const [members, setMembers] = useState<Member[]>([])
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    if (isOpen) {
      loadMembers()
    }
  }, [isOpen])

  useEffect(() => {
    filterMembers()
  }, [searchQuery, members])

  const loadMembers = () => {
    try {
      // Load members from localStorage
      const storedMembers = localStorage.getItem('gym_members')
      if (storedMembers) {
        const memberData = JSON.parse(storedMembers)
        setMembers(memberData)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load members',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const filterMembers = () => {
    if (!searchQuery.trim()) {
      setFilteredMembers(members)
      return
    }

    const query = searchQuery.toLowerCase()
    const filtered = members.filter(member =>
      member.full_name.toLowerCase().includes(query) ||
      member.phone.includes(query) ||
      (member.email && member.email.toLowerCase().includes(query))
    )
    setFilteredMembers(filtered)
  }

  const handleSelectMember = (member: Member) => {
    onSelectMember(member)
    onClose()
    setSearchQuery('')
  }

  const handleClose = () => {
    onClose()
    setSearchQuery('')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[80vh] flex flex-col">
        <CardHeader className="pb-3 flex-shrink-0">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span>{t('select_member')}</span>
            </span>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
            >
              <X className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Search */}
          <div className="relative flex-shrink-0">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={t('member_search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>

          {/* Guest Customer Option */}
          <div className="flex-shrink-0">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleSelectMember({
                id: 'guest',
                full_name: t('guest_customer'),
                phone: '',
                email: '',
                gender: 'male',
                age: 0,
                situation: 'guest'
              })}
            >
              <User className="w-4 h-4 mr-2" />
              {t('guest_customer')}
            </Button>
          </div>

          {/* Members List */}
          <div className="flex-1 overflow-auto">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
              </div>
            ) : filteredMembers.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>{searchQuery ? 'No members found' : 'No members available'}</p>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredMembers.map((member) => (
                  <div
                    key={member.id}
                    className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                    onClick={() => handleSelectMember(member)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {member.full_name}
                        </h3>
                        <div className="flex items-center space-x-4 mt-1">
                          <div className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
                            <Phone className="w-3 h-3" />
                            <span>{member.phone}</span>
                          </div>
                          {member.email && (
                            <div className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
                              <Mail className="w-3 h-3" />
                              <span>{member.email}</span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">
                            {t(member.gender)}
                          </span>
                          <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">
                            {member.age} years
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="gym"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleSelectMember(member)
                        }}
                      >
                        {t('select')}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
