"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/inventory/add-product-modal.tsx":
/*!********************************************************!*\
  !*** ./src/components/inventory/add-product-modal.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddProductModal: function() { return /* binding */ AddProductModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ AddProductModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction AddProductModal(param) {\n    let { product, onClose, onSave } = param;\n    _s();\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Form state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        category: \"\",\n        price_dzd: \"\",\n        stock: \"\",\n        min_stock: \"5\",\n        expiry_date: \"\",\n        image_url: \"\",\n        barcode: \"\",\n        qr_code: \"\",\n        description: \"\",\n        brand: \"\",\n        unit: \"piece\"\n    });\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load categories and populate form if editing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const categoriesData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getCategories();\n        setCategories(categoriesData.filter((c)=>c.active));\n        if (product) {\n            setFormData({\n                name: product.name || \"\",\n                category: product.category || \"\",\n                price_dzd: (product.price_dzd || 0).toString(),\n                stock: (product.stock || 0).toString(),\n                min_stock: (product.min_stock || 5).toString(),\n                expiry_date: product.expiry_date || \"\",\n                image_url: product.image_url || \"\",\n                barcode: product.barcode || \"\",\n                qr_code: product.qr_code || \"\",\n                description: product.description || \"\",\n                brand: product.brand || \"\",\n                unit: product.unit || \"piece\"\n            });\n        }\n    }, [\n        product\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleScanBarcode = ()=>{\n        setShowScanner(true);\n    };\n    const handleScanSuccess = (scannedCode)=>{\n        setFormData((prev)=>({\n                ...prev,\n                barcode: scannedCode\n            }));\n        toast({\n            title: \"Barcode Scanned\",\n            description: \"Barcode: \".concat(scannedCode)\n        });\n        setShowScanner(false);\n    };\n    const handleImageUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // For now, we'll create a local URL\n            // In a real implementation, you would upload to a server or cloud storage\n            const imageUrl = URL.createObjectURL(file);\n            setFormData((prev)=>({\n                    ...prev,\n                    image_url: imageUrl\n                }));\n            toast({\n                title: \"Image Uploaded\",\n                description: \"Product image has been uploaded successfully\"\n            });\n        }\n    };\n    const validateForm = ()=>{\n        if (!formData.name.trim()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Product name is required\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (!formData.category) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Category is required\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (!formData.price_dzd || parseFloat(formData.price_dzd) <= 0) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Valid price is required\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (!formData.stock || parseInt(formData.stock) < 0) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Valid stock quantity is required\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        if (!validateForm()) return;\n        setIsLoading(true);\n        try {\n            const productData = {\n                name: formData.name.trim(),\n                category: formData.category,\n                price_dzd: parseFloat(formData.price_dzd),\n                stock: parseInt(formData.stock),\n                min_stock: parseInt(formData.min_stock),\n                expiry_date: formData.expiry_date || undefined,\n                image_url: formData.image_url || undefined,\n                barcode: formData.barcode || undefined,\n                qr_code: formData.qr_code || undefined,\n                description: formData.description || undefined,\n                brand: formData.brand || undefined,\n                unit: formData.unit\n            };\n            if (product) {\n                // Update existing product\n                const products = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getFromStorage(\"gym_products\");\n                const index = products.findIndex((p)=>p.id === product.id);\n                if (index !== -1) {\n                    products[index] = {\n                        ...products[index],\n                        ...productData,\n                        updated_at: new Date().toISOString()\n                    };\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.saveToStorage(\"gym_products\", products);\n                }\n            } else {\n                // Add new product\n                const products = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getFromStorage(\"gym_products\");\n                const newProduct = {\n                    ...productData,\n                    id: Date.now().toString(36) + Math.random().toString(36).substr(2),\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                };\n                products.push(newProduct);\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.saveToStorage(\"gym_products\", products);\n            }\n            toast({\n                title: \"Success\",\n                description: \"Product \".concat(product ? \"updated\" : \"added\", \" successfully\")\n            });\n            onSave();\n            onClose();\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save product\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-4xl max-h-[90vh] overflow-y-auto glass border-white/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: product ? \"Edit Product\" : \"Add New Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Basic Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Product Name *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.name,\n                                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                                                placeholder: \"Enter product name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Category *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.category,\n                                                                onChange: (e)=>handleInputChange(\"category\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Select category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category.name,\n                                                                            children: category.name\n                                                                        }, category.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Brand\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.brand,\n                                                                onChange: (e)=>handleInputChange(\"brand\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                                                placeholder: \"Enter brand name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Description\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: formData.description,\n                                                                onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                rows: 3,\n                                                                placeholder: \"Enter product description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Pricing & Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4 inline mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 314,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \"Price (DZD) *\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: formData.price_dzd,\n                                                                        onChange: (e)=>handleInputChange(\"price_dzd\", e.target.value),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        placeholder: \"0.00\",\n                                                                        min: \"0\",\n                                                                        step: \"0.01\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"w-4 h-4 inline mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \"Unit\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: formData.unit,\n                                                                        onChange: (e)=>handleInputChange(\"unit\", e.target.value),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"piece\",\n                                                                                children: \"Piece\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"kg\",\n                                                                                children: \"Kilogram\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"liter\",\n                                                                                children: \"Liter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"box\",\n                                                                                children: \"Box\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 341,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"bottle\",\n                                                                                children: \"Bottle\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 342,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"pack\",\n                                                                                children: \"Pack\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: \"Current Stock *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: formData.stock,\n                                                                        onChange: (e)=>handleInputChange(\"stock\", e.target.value),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        placeholder: \"0\",\n                                                                        min: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: \"Minimum Stock\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: formData.min_stock,\n                                                                        onChange: (e)=>handleInputChange(\"min_stock\", e.target.value),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        placeholder: \"5\",\n                                                                        min: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Expiry Date\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.expiry_date,\n                                                                onChange: (e)=>handleInputChange(\"expiry_date\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Product Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                        children: formData.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: formData.image_url,\n                                                                    alt: \"Product preview\",\n                                                                    className: \"w-32 h-32 object-cover rounded-lg mx-auto\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>handleInputChange(\"image_url\", \"\"),\n                                                                    children: \"Remove Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-12 h-12 text-gray-400 mx-auto\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"cursor-pointer\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"file\",\n                                                                                accept: \"image/*\",\n                                                                                onChange: handleImageUpload,\n                                                                                className: \"hidden\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 419,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Upload Image\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                    lineNumber: 426,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Identification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Barcode\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: formData.barcode,\n                                                                        onChange: (e)=>handleInputChange(\"barcode\", e.target.value),\n                                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        placeholder: \"Enter or scan barcode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"outline\",\n                                                                        onClick: handleScanBarcode,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                            lineNumber: 457,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"QR Code\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.qr_code,\n                                                                onChange: (e)=>handleInputChange(\"qr_code\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                placeholder: \"Enter QR code\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        onClick: onClose,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSave,\n                                        disabled: isLoading,\n                                        className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this),\n                                            isLoading ? \"Saving...\" : product ? \"Update Product\" : \"Add Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_7__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: \"barcode\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s(AddProductModal, \"PbW2vU2V8Iaib6AnJieGpPmv/+Q=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = AddProductModal;\nvar _c;\n$RefreshReg$(_c, \"AddProductModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/inventory/add-product-modal.tsx\n"));

/***/ })

});