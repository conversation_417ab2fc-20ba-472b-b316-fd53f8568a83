'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import { InventoryStorage, Supplier, ExtendedProduct, Purchase, PurchaseItem } from '@/lib/inventory-storage'
import { formatCurrency } from '@/lib/utils'
import {
  X,
  Save,
  Plus,
  Minus,
  Trash2,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  FileText,
  DollarSign,
  Hash,
  Search,
} from 'lucide-react'

interface PurchaseModalProps {
  onClose: () => void
  onSave: () => void
  suppliers: Supplier[]
  products: ExtendedProduct[]
}

interface PurchaseItemForm {
  product_id: string
  product_name: string
  quantity: number
  unit_cost: number
  total_cost: number
  expiry_date?: string
}

export function PurchaseModal({ onClose, onSave, suppliers, products }: PurchaseModalProps) {
  const { t } = useLanguage()
  const { toast } = useToast()

  // Form state
  const [formData, setFormData] = useState({
    supplier_id: '',
    invoice_number: '',
    purchase_date: new Date().toISOString().split('T')[0],
    payment_type: 'cash' as const,
    due_date: '',
    notes: '',
  })

  const [purchaseItems, setPurchaseItems] = useState<PurchaseItemForm[]>([])
  const [selectedProduct, setSelectedProduct] = useState('')
  const [productSearch, setProductSearch] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Calculate totals
  const totalAmount = purchaseItems.reduce((sum, item) => sum + item.total_cost, 0)

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
    product.category.toLowerCase().includes(productSearch.toLowerCase())
  )

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addProductToPurchase = () => {
    if (!selectedProduct) {
      toast({
        title: 'Validation Error',
        description: 'Please select a product',
        variant: 'destructive',
      })
      return
    }

    const product = products.find(p => p.id === selectedProduct)
    if (!product) return

    // Check if product already exists in purchase
    const existingIndex = purchaseItems.findIndex(item => item.product_id === selectedProduct)
    if (existingIndex !== -1) {
      toast({
        title: 'Product Already Added',
        description: 'This product is already in the purchase list',
        variant: 'destructive',
      })
      return
    }

    const newItem: PurchaseItemForm = {
      product_id: product.id,
      product_name: product.name,
      quantity: 1,
      unit_cost: product.price_dzd,
      total_cost: product.price_dzd,
      expiry_date: '',
    }

    setPurchaseItems(prev => [...prev, newItem])
    setSelectedProduct('')
    setProductSearch('')
  }

  const updatePurchaseItem = (index: number, field: keyof PurchaseItemForm, value: string | number) => {
    setPurchaseItems(prev => {
      const updated = [...prev]
      updated[index] = {
        ...updated[index],
        [field]: value
      }

      // Recalculate total cost when quantity or unit cost changes
      if (field === 'quantity' || field === 'unit_cost') {
        updated[index].total_cost = updated[index].quantity * updated[index].unit_cost
      }

      return updated
    })
  }

  const removePurchaseItem = (index: number) => {
    setPurchaseItems(prev => prev.filter((_, i) => i !== index))
  }

  const validateForm = () => {
    if (!formData.supplier_id) {
      toast({
        title: 'Validation Error',
        description: 'Please select a supplier',
        variant: 'destructive',
      })
      return false
    }

    if (purchaseItems.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'Please add at least one product',
        variant: 'destructive',
      })
      return false
    }

    if (formData.payment_type === 'credit' && !formData.due_date) {
      toast({
        title: 'Validation Error',
        description: 'Due date is required for credit purchases',
        variant: 'destructive',
      })
      return false
    }

    // Validate all purchase items
    for (let i = 0; i < purchaseItems.length; i++) {
      const item = purchaseItems[i]
      if (item.quantity <= 0) {
        toast({
          title: 'Validation Error',
          description: `Invalid quantity for ${item.product_name}`,
          variant: 'destructive',
        })
        return false
      }
      if (item.unit_cost <= 0) {
        toast({
          title: 'Validation Error',
          description: `Invalid unit cost for ${item.product_name}`,
          variant: 'destructive',
        })
        return false
      }
    }

    return true
  }

  const handleSave = async () => {
    if (!validateForm()) return

    setIsLoading(true)
    try {
      // Create purchase record
      const purchaseData: Omit<Purchase, 'id' | 'created_at' | 'updated_at'> = {
        supplier_id: formData.supplier_id,
        invoice_number: formData.invoice_number || undefined,
        purchase_date: formData.purchase_date,
        payment_type: formData.payment_type,
        total_amount: totalAmount,
        paid_amount: formData.payment_type === 'cash' ? totalAmount : 0,
        remaining_balance: formData.payment_type === 'cash' ? 0 : totalAmount,
        payment_status: formData.payment_type === 'cash' ? 'paid' : 'unpaid',
        due_date: formData.due_date || undefined,
        notes: formData.notes || undefined,
      }

      const purchase = InventoryStorage.addPurchase(purchaseData)

      // Create purchase items and update stock
      for (const item of purchaseItems) {
        const purchaseItemData: Omit<PurchaseItem, 'id' | 'created_at'> = {
          purchase_id: purchase.id,
          product_id: item.product_id,
          product_name: item.product_name,
          quantity: item.quantity,
          unit_cost: item.unit_cost,
          total_cost: item.total_cost,
          expiry_date: item.expiry_date || undefined,
        }

        InventoryStorage.addPurchaseItem(purchaseItemData)
      }

      toast({
        title: 'Success',
        description: 'Purchase recorded successfully',
      })

      onSave()
      onClose()
    } catch (error) {
      console.error('Error saving purchase:', error)
      toast({
        title: 'Error',
        description: 'Failed to save purchase',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-y-auto glass border-white/20">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <ShoppingCart className="w-6 h-6" />
            <span>New Purchase</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Purchase Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                <Users className="w-4 h-4 inline mr-2" />
                Supplier *
              </label>
              <select
                value={formData.supplier_id}
                onChange={(e) => handleInputChange('supplier_id', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select supplier</option>
                {suppliers.map(supplier => (
                  <option key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                <Hash className="w-4 h-4 inline mr-2" />
                Invoice Number
              </label>
              <input
                type="text"
                value={formData.invoice_number}
                onChange={(e) => handleInputChange('invoice_number', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="Enter invoice number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                <Calendar className="w-4 h-4 inline mr-2" />
                Purchase Date *
              </label>
              <input
                type="date"
                value={formData.purchase_date}
                onChange={(e) => handleInputChange('purchase_date', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                <DollarSign className="w-4 h-4 inline mr-2" />
                Payment Type *
              </label>
              <select
                value={formData.payment_type}
                onChange={(e) => handleInputChange('payment_type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="cash">Cash</option>
                <option value="credit">Credit</option>
              </select>
            </div>
          </div>

          {/* Due Date for Credit */}
          {formData.payment_type === 'credit' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Due Date *
                </label>
                <input
                  type="date"
                  value={formData.due_date}
                  onChange={(e) => handleInputChange('due_date', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  <FileText className="w-4 h-4 inline mr-2" />
                  Notes
                </label>
                <input
                  type="text"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter notes"
                />
              </div>
            </div>
          )}

          {/* Add Products Section */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">Add Products</h3>
            
            <div className="flex space-x-4 mb-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search products..."
                    value={productSearch}
                    onChange={(e) => setProductSearch(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              <select
                value={selectedProduct}
                onChange={(e) => setSelectedProduct(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 min-w-64"
              >
                <option value="">Select product</option>
                {filteredProducts.map(product => (
                  <option key={product.id} value={product.id}>
                    {product.name} - {product.category}
                  </option>
                ))}
              </select>
              <Button
                onClick={addProductToPurchase}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add
              </Button>
            </div>
          </div>

          {/* Purchase Items Table */}
          {purchaseItems.length > 0 && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold mb-4">Purchase Items</h3>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4">Product</th>
                      <th className="text-left py-3 px-4">Quantity</th>
                      <th className="text-left py-3 px-4">Unit Cost</th>
                      <th className="text-left py-3 px-4">Total</th>
                      <th className="text-left py-3 px-4">Expiry Date</th>
                      <th className="text-left py-3 px-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {purchaseItems.map((item, index) => (
                      <tr key={index} className="border-b border-gray-100">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium">{item.product_name}</p>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updatePurchaseItem(index, 'quantity', Math.max(1, item.quantity - 1))}
                            >
                              <Minus className="w-3 h-3" />
                            </Button>
                            <input
                              type="number"
                              value={item.quantity}
                              onChange={(e) => updatePurchaseItem(index, 'quantity', parseInt(e.target.value) || 1)}
                              className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
                              min="1"
                            />
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updatePurchaseItem(index, 'quantity', item.quantity + 1)}
                            >
                              <Plus className="w-3 h-3" />
                            </Button>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <input
                            type="number"
                            value={item.unit_cost}
                            onChange={(e) => updatePurchaseItem(index, 'unit_cost', parseFloat(e.target.value) || 0)}
                            className="w-24 px-2 py-1 border border-gray-300 rounded"
                            min="0"
                            step="0.01"
                          />
                        </td>
                        <td className="py-3 px-4">
                          <span className="font-medium">
                            {formatCurrency(item.total_cost)}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <input
                            type="date"
                            value={item.expiry_date || ''}
                            onChange={(e) => updatePurchaseItem(index, 'expiry_date', e.target.value)}
                            className="px-2 py-1 border border-gray-300 rounded"
                          />
                        </td>
                        <td className="py-3 px-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removePurchaseItem(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Total */}
              <div className="flex justify-end mt-4 pt-4 border-t">
                <div className="text-right">
                  <p className="text-lg font-semibold">
                    Total Amount: {formatCurrency(totalAmount)}
                  </p>
                  {formData.payment_type === 'credit' && (
                    <p className="text-sm text-gray-600">
                      Amount will be added to supplier balance
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSave}
              disabled={isLoading || purchaseItems.length === 0}
              className="bg-green-500 hover:bg-green-600 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? 'Saving...' : 'Save Purchase'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
