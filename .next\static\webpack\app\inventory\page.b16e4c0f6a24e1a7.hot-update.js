"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InventoryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_inventory_add_product_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/inventory/add-product-modal */ \"(app-pages-browser)/./src/components/inventory/add-product-modal.tsx\");\n/* harmony import */ var _components_inventory_suppliers_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/inventory/suppliers-modal */ \"(app-pages-browser)/./src/components/inventory/suppliers-modal.tsx\");\n/* harmony import */ var _components_inventory_purchase_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/inventory/purchase-modal */ \"(app-pages-browser)/./src/components/inventory/purchase-modal.tsx\");\n/* harmony import */ var _components_inventory_stock_movement_history__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/inventory/stock-movement-history */ \"(app-pages-browser)/./src/components/inventory/stock-movement-history.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Import components (we'll create these)\n\n\n\n\nfunction InventoryPage() {\n    _s();\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // State management\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stockMovements, setStockMovements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showLowStock, setShowLowStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExpiring, setShowExpiring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modal states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuppliers, setShowSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPurchase, setShowPurchase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStockHistory, setShowStockHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStockAdjustment, setShowStockAdjustment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n        _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.initializeDefaultData();\n    }, []);\n    const loadData = ()=>{\n        try {\n            const productsData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getFromStorage(\"gym_products\");\n            const suppliersData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getSuppliers();\n            const movementsData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getStockMovements();\n            setProducts(productsData);\n            setSuppliers(suppliersData.filter((s)=>s.active));\n            setStockMovements(movementsData);\n        } catch (error) {\n            console.error(\"Error loading inventory data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load inventory data\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Filter products based on search and filters\n    const filteredProducts = products.filter((product)=>{\n        var _product_brand;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || product.category === selectedCategory;\n        const matchesLowStock = !showLowStock || product.stock <= product.min_stock;\n        const matchesExpiring = !showExpiring || product.expiry_date && new Date(product.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n        return matchesSearch && matchesCategory && (!showLowStock || matchesLowStock) && (!showExpiring || matchesExpiring);\n    });\n    // Calculate statistics\n    const stats = {\n        totalProducts: products.length,\n        totalValue: products.reduce((sum, p)=>sum + p.stock * p.price_dzd, 0),\n        lowStockCount: products.filter((p)=>p.stock <= p.min_stock).length,\n        expiringCount: products.filter((p)=>p.expiry_date && new Date(p.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)).length,\n        activeSuppliers: suppliers.length\n    };\n    const categories = [\n        ...new Set(products.map((p)=>p.category))\n    ];\n    const handleProductUpdate = ()=>{\n        loadData();\n        setSelectedProduct(null);\n    };\n    const handleDeleteProduct = (productId)=>{\n        if (confirm(\"Are you sure you want to delete this product?\")) {\n            const updatedProducts = products.filter((p)=>p.id !== productId);\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            loadData();\n            toast({\n                title: \"Success\",\n                description: \"Product deleted successfully\"\n            });\n        }\n    };\n    const getStockStatus = (product)=>{\n        if (product.stock === 0) return {\n            status: \"out\",\n            color: \"text-red-500\",\n            bg: \"bg-red-50\"\n        };\n        if (product.stock <= product.min_stock) return {\n            status: \"low\",\n            color: \"text-orange-500\",\n            bg: \"bg-orange-50\"\n        };\n        return {\n            status: \"good\",\n            color: \"text-green-500\",\n            bg: \"bg-green-50\"\n        };\n    };\n    const getExpiryStatus = (expiryDate)=>{\n        if (!expiryDate) return null;\n        const today = new Date();\n        const expiry = new Date(expiryDate);\n        const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (daysUntilExpiry < 0) return {\n            status: \"expired\",\n            color: \"text-red-500\",\n            text: \"Expired\"\n        };\n        if (daysUntilExpiry <= 7) return {\n            status: \"expiring\",\n            color: \"text-orange-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n        if (daysUntilExpiry <= 30) return {\n            status: \"warning\",\n            color: \"text-yellow-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n        return {\n            status: \"good\",\n            color: \"text-green-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: \"Inventory Management\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-8 h-8 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.totalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(stats.totalValue)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-8 h-8 text-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Low Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.lowStockCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-8 h-8 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Expiring Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.expiringCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-8 h-8 text-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Suppliers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.activeSuppliers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowAddProduct(true),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add New Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowPurchase(true),\n                                className: \"bg-green-500 hover:bg-green-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"New Purchase\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowSuppliers(true),\n                                className: \"bg-purple-500 hover:bg-purple-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Manage Suppliers\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowStockHistory(true),\n                                variant: \"outline\",\n                                className: \"border-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Stock History\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"glass border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1 min-w-64\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search products...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: showLowStock ? \"default\" : \"outline\",\n                                        onClick: ()=>setShowLowStock(!showLowStock),\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Low Stock\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: showExpiring ? \"default\" : \"outline\",\n                                        onClick: ()=>setShowExpiring(!showExpiring),\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Expiring\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"glass border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Products (\",\n                                                filteredProducts.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Export\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Product\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Stock\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Price\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Expiry\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: filteredProducts.map((product)=>{\n                                                        const stockStatus = getStockStatus(product);\n                                                        const expiryStatus = getExpiryStatus(product.expiry_date);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-100 hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            product.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: product.image_url,\n                                                                                alt: product.name,\n                                                                                className: \"w-10 h-10 rounded-lg object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 333,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 340,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: product.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                        lineNumber: 344,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    product.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: product.brand\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                        lineNumber: 346,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 bg-gray-100 rounded-full text-sm\",\n                                                                        children: product.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium \".concat(stockStatus.color),\n                                                                                children: [\n                                                                                    product.stock,\n                                                                                    \" \",\n                                                                                    product.unit\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            stockStatus.status !== \"good\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-orange-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(product.price_dzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: expiryStatus ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: expiryStatus.color,\n                                                                        children: expiryStatus.text\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"No expiry\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>setSelectedProduct(product),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 385,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 380,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedProduct(product);\n                                                                                    setShowAddProduct(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 395,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleDeleteProduct(product.id),\n                                                                                className: \"text-red-500 hover:text-red-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 403,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 397,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, product.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: \"No products found matching your criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            showAddProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_add_product_modal__WEBPACK_IMPORTED_MODULE_9__.AddProductModal, {\n                product: selectedProduct,\n                onClose: ()=>{\n                    setShowAddProduct(false);\n                    setSelectedProduct(null);\n                },\n                onSave: handleProductUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 425,\n                columnNumber: 9\n            }, this),\n            showSuppliers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_suppliers_modal__WEBPACK_IMPORTED_MODULE_10__.SuppliersModal, {\n                onClose: ()=>setShowSuppliers(false),\n                onUpdate: loadData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 436,\n                columnNumber: 9\n            }, this),\n            showPurchase && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_purchase_modal__WEBPACK_IMPORTED_MODULE_11__.PurchaseModal, {\n                onClose: ()=>setShowPurchase(false),\n                onSave: handleProductUpdate,\n                suppliers: suppliers,\n                products: products\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 443,\n                columnNumber: 9\n            }, this),\n            showStockHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_stock_movement_history__WEBPACK_IMPORTED_MODULE_12__.StockMovementHistory, {\n                onClose: ()=>setShowStockHistory(false),\n                movements: stockMovements,\n                products: products\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 452,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryPage, \"FtTF1IVUGLVI/uQjhooKAISURtE=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ })

});