"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/app/inventory/page.tsx":
/*!************************************!*\
  !*** ./src/app/inventory/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InventoryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Download,Edit,Eye,FileText,Image,Package,Plus,Search,ShoppingCart,Trash2,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_inventory_add_product_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/inventory/add-product-modal */ \"(app-pages-browser)/./src/components/inventory/add-product-modal.tsx\");\n/* harmony import */ var _components_inventory_suppliers_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/inventory/suppliers-modal */ \"(app-pages-browser)/./src/components/inventory/suppliers-modal.tsx\");\n/* harmony import */ var _components_inventory_purchase_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/inventory/purchase-modal */ \"(app-pages-browser)/./src/components/inventory/purchase-modal.tsx\");\n/* harmony import */ var _components_inventory_stock_movement_history__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/inventory/stock-movement-history */ \"(app-pages-browser)/./src/components/inventory/stock-movement-history.tsx\");\n/* harmony import */ var _components_inventory_stock_adjustment_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/inventory/stock-adjustment-modal */ \"(app-pages-browser)/./src/components/inventory/stock-adjustment-modal.tsx\");\n/* harmony import */ var _components_inventory_inventory_reports__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/inventory/inventory-reports */ \"(app-pages-browser)/./src/components/inventory/inventory-reports.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Import components (we'll create these)\n\n\n\n\n\n\nfunction InventoryPage() {\n    _s();\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // State management\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stockMovements, setStockMovements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showLowStock, setShowLowStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExpiring, setShowExpiring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modal states\n    const [showAddProduct, setShowAddProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuppliers, setShowSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPurchase, setShowPurchase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStockHistory, setShowStockHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStockAdjustment, setShowStockAdjustment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReports, setShowReports] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductDetails, setShowProductDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n        _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.initializeDefaultData();\n    }, []);\n    const loadData = ()=>{\n        try {\n            const productsData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getFromStorage(\"gym_products\");\n            const suppliersData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getSuppliers();\n            const movementsData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.getStockMovements();\n            setProducts(productsData);\n            setSuppliers(suppliersData.filter((s)=>s.active));\n            setStockMovements(movementsData);\n        } catch (error) {\n            console.error(\"Error loading inventory data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load inventory data\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Filter products based on search and filters\n    const filteredProducts = products.filter((product)=>{\n        var _product_brand;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || product.category === selectedCategory;\n        const matchesLowStock = !showLowStock || product.stock <= product.min_stock;\n        const matchesExpiring = !showExpiring || product.expiry_date && new Date(product.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n        return matchesSearch && matchesCategory && (!showLowStock || matchesLowStock) && (!showExpiring || matchesExpiring);\n    });\n    // Calculate statistics\n    const stats = {\n        totalProducts: products.length,\n        totalValue: products.reduce((sum, p)=>sum + p.stock * p.price_dzd, 0),\n        lowStockCount: products.filter((p)=>p.stock <= p.min_stock).length,\n        expiringCount: products.filter((p)=>p.expiry_date && new Date(p.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)).length,\n        activeSuppliers: suppliers.length\n    };\n    const categories = [\n        ...new Set(products.map((p)=>p.category))\n    ];\n    const handleProductUpdate = ()=>{\n        loadData();\n        setSelectedProduct(null);\n    };\n    const handleDeleteProduct = (productId)=>{\n        if (confirm(\"Are you sure you want to delete this product?\")) {\n            const updatedProducts = products.filter((p)=>p.id !== productId);\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_7__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            loadData();\n            toast({\n                title: \"Success\",\n                description: \"Product deleted successfully\"\n            });\n        }\n    };\n    const getStockStatus = (product)=>{\n        if (product.stock === 0) return {\n            status: \"out\",\n            color: \"text-red-500\",\n            bg: \"bg-red-50\"\n        };\n        if (product.stock <= product.min_stock) return {\n            status: \"low\",\n            color: \"text-orange-500\",\n            bg: \"bg-orange-50\"\n        };\n        return {\n            status: \"good\",\n            color: \"text-green-500\",\n            bg: \"bg-green-50\"\n        };\n    };\n    const getExpiryStatus = (expiryDate)=>{\n        if (!expiryDate) return null;\n        const today = new Date();\n        const expiry = new Date(expiryDate);\n        const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (daysUntilExpiry < 0) return {\n            status: \"expired\",\n            color: \"text-red-500\",\n            text: \"Expired\"\n        };\n        if (daysUntilExpiry <= 7) return {\n            status: \"expiring\",\n            color: \"text-orange-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n        if (daysUntilExpiry <= 30) return {\n            status: \"warning\",\n            color: \"text-yellow-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n        return {\n            status: \"good\",\n            color: \"text-green-500\",\n            text: \"\".concat(daysUntilExpiry, \" days\")\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: \"Inventory Management\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-8 h-8 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.totalProducts\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-8 h-8 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Total Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(stats.totalValue)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-8 h-8 text-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Low Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.lowStockCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-8 h-8 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Expiring Soon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.expiringCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-8 h-8 text-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Suppliers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: stats.activeSuppliers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowAddProduct(true),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add New Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowPurchase(true),\n                                className: \"bg-green-500 hover:bg-green-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"New Purchase\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowSuppliers(true),\n                                className: \"bg-purple-500 hover:bg-purple-600 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Manage Suppliers\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowStockHistory(true),\n                                variant: \"outline\",\n                                className: \"border-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Stock History\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setShowReports(true),\n                                variant: \"outline\",\n                                className: \"border-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reports\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"glass border-white/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex-1 min-w-64\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search products...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: showLowStock ? \"default\" : \"outline\",\n                                        onClick: ()=>setShowLowStock(!showLowStock),\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Low Stock\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: showExpiring ? \"default\" : \"outline\",\n                                        onClick: ()=>setShowExpiring(!showExpiring),\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Expiring\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"glass border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Products (\",\n                                                filteredProducts.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Export\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Product\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Stock\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Price\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Expiry\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: filteredProducts.map((product)=>{\n                                                        const stockStatus = getStockStatus(product);\n                                                        const expiryStatus = getExpiryStatus(product.expiry_date);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-100 hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            product.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: product.image_url,\n                                                                                alt: product.name,\n                                                                                className: \"w-10 h-10 rounded-lg object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: product.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                        lineNumber: 358,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    product.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-500\",\n                                                                                        children: product.brand\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                        lineNumber: 360,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 357,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 bg-gray-100 rounded-full text-sm\",\n                                                                        children: product.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium \".concat(stockStatus.color),\n                                                                                children: [\n                                                                                    product.stock,\n                                                                                    \" \",\n                                                                                    product.unit\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            stockStatus.status !== \"good\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-orange-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 376,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(product.price_dzd)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: expiryStatus ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: expiryStatus.color,\n                                                                        children: expiryStatus.text\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"No expiry\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedProduct(product);\n                                                                                    setShowProductDetails(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 402,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 394,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedProduct(product);\n                                                                                    setShowAddProduct(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 412,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedProduct(product);\n                                                                                    setShowStockAdjustment(true);\n                                                                                },\n                                                                                className: \"text-orange-500 hover:text-orange-700\",\n                                                                                title: \"Adjust Stock\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 424,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleDeleteProduct(product.id),\n                                                                                className: \"text-red-500 hover:text-red-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Download_Edit_Eye_FileText_Image_Package_Plus_Search_ShoppingCart_Trash2_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                    lineNumber: 432,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, product.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: \"No products found matching your criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            showAddProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_add_product_modal__WEBPACK_IMPORTED_MODULE_9__.AddProductModal, {\n                product: selectedProduct,\n                onClose: ()=>{\n                    setShowAddProduct(false);\n                    setSelectedProduct(null);\n                },\n                onSave: handleProductUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 454,\n                columnNumber: 9\n            }, this),\n            showSuppliers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_suppliers_modal__WEBPACK_IMPORTED_MODULE_10__.SuppliersModal, {\n                onClose: ()=>setShowSuppliers(false),\n                onUpdate: loadData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 465,\n                columnNumber: 9\n            }, this),\n            showPurchase && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_purchase_modal__WEBPACK_IMPORTED_MODULE_11__.PurchaseModal, {\n                onClose: ()=>setShowPurchase(false),\n                onSave: handleProductUpdate,\n                suppliers: suppliers,\n                products: products\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 472,\n                columnNumber: 9\n            }, this),\n            showStockHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_stock_movement_history__WEBPACK_IMPORTED_MODULE_12__.StockMovementHistory, {\n                onClose: ()=>setShowStockHistory(false),\n                movements: stockMovements,\n                products: products\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 481,\n                columnNumber: 9\n            }, this),\n            showStockAdjustment && selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_stock_adjustment_modal__WEBPACK_IMPORTED_MODULE_13__.StockAdjustmentModal, {\n                product: selectedProduct,\n                onClose: ()=>{\n                    setShowStockAdjustment(false);\n                    setSelectedProduct(null);\n                },\n                onSave: handleProductUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this),\n            showReports && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_inventory_reports__WEBPACK_IMPORTED_MODULE_14__.InventoryReports, {\n                onClose: ()=>setShowReports(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n                lineNumber: 500,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\inventory\\\\page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryPage, \"ThUGHa5BmLAq0GQXQkgtzdI7i6E=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = InventoryPage;\nvar _c;\n$RefreshReg$(_c, \"InventoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/inventory/page.tsx\n"));

/***/ })

});