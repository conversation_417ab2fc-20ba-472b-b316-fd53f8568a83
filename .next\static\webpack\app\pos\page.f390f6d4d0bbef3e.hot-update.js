"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Try to load from Supabase first\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n            if (error) throw error;\n            setProducts(data || []);\n        } catch (error) {\n            // Fallback to localStorage for development\n            try {\n                const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n                if (storedProducts.length > 0) {\n                    const productData = storedProducts.filter((p)=>p.stock > 0);\n                    setProducts(productData.map((p)=>({\n                            id: p.id,\n                            name: p.name,\n                            category: p.category,\n                            price_dzd: p.price_dzd,\n                            stock: p.stock,\n                            image_url: p.image_url,\n                            barcode: p.barcode,\n                            qr_code: p.qr_code,\n                            expiry_date: p.expiry_date\n                        })));\n                } else {\n                    // Create sample products for development\n                    const sampleProducts = [\n                        {\n                            id: \"1\",\n                            name: \"Protein Powder\",\n                            category: \"Supplements\",\n                            price_dzd: 5000,\n                            stock: 25,\n                            barcode: \"1234567890123\",\n                            expiry_date: \"2024-12-31\"\n                        },\n                        {\n                            id: \"2\",\n                            name: \"Energy Drink\",\n                            category: \"Beverages\",\n                            price_dzd: 200,\n                            stock: 50,\n                            barcode: \"2345678901234\",\n                            expiry_date: \"2024-06-30\"\n                        },\n                        {\n                            id: \"3\",\n                            name: \"Gym Towel\",\n                            category: \"Accessories\",\n                            price_dzd: 800,\n                            stock: 15,\n                            barcode: \"3456789012345\"\n                        }\n                    ];\n                    setProducts(sampleProducts);\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.saveToStorage(\"gym_products\", sampleProducts.map((p)=>({\n                            ...p,\n                            min_stock: 5,\n                            description: \"\",\n                            brand: \"\",\n                            unit: \"piece\",\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        })));\n                }\n            } catch (localError) {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to load products\",\n                    variant: \"destructive\"\n                });\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Update product stock in localStorage using InventoryStorage\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            const updatedProducts = storedProducts.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    // Create stock movement record\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.addStockMovement({\n                        product_id: product.id,\n                        product_name: product.name,\n                        movement_type: \"sale\",\n                        quantity_change: -cartItem.quantity,\n                        previous_stock: product.stock,\n                        new_stock: product.stock - cartItem.quantity,\n                        reference_id: transaction.id,\n                        reference_type: \"sale\",\n                        notes: \"Sale transaction - \".concat(cartItem.quantity, \" units sold\")\n                    });\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity,\n                        updated_at: new Date().toISOString()\n                    };\n                }\n                return product;\n            });\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 overflow-auto max-h-[calc(100vh-16rem)]\",\n                                children: filteredProducts.map((product)=>{\n                                    const expired = isProductExpired(product);\n                                    const expiringSoon = isProductExpiringSoon(product);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\"),\n                                        onClick: ()=>!expired && addToCart(product),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-gray-900 dark:text-white line-clamp-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    t(\"expiry_date\"),\n                                                                    \": \",\n                                                                    new Date(product.expiry_date).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-red-600 dark:text-red-400\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 526,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            t(\"stock\"),\n                                                                            \": \",\n                                                                            product.stock\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 652,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 673,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 698,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 695,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 760,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 768,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 422,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ })

});