'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency } from '@/lib/utils'
import {
  AlertTriangle,
  Calendar,
  Package,
  Edit,
  Save,
  X,
  Clock,
} from 'lucide-react'

interface Product {
  id: string
  name: string
  category: string
  price_dzd: number
  stock: number
  expiry_date?: string
  barcode?: string
}

interface ProductExpiryManagerProps {
  products: Product[]
  onUpdateProduct: (product: Product) => void
}

export function ProductExpiryManager({ products, onUpdateProduct }: ProductExpiryManagerProps) {
  const [editingProduct, setEditingProduct] = useState<string | null>(null)
  const [expiryDate, setExpiryDate] = useState('')
  const { t } = useLanguage()
  const { toast } = useToast()

  const getExpiryStatus = (product: Product) => {
    if (!product.expiry_date) return 'none'
    
    const today = new Date()
    const expiry = new Date(product.expiry_date)
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysUntilExpiry < 0) return 'expired'
    if (daysUntilExpiry <= 7) return 'expiring'
    return 'good'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'expired': return 'text-red-500 bg-red-50 dark:bg-red-900/20'
      case 'expiring': return 'text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
      case 'good': return 'text-green-500 bg-green-50 dark:bg-green-900/20'
      default: return 'text-gray-500 bg-gray-50 dark:bg-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'expired': return t('product_expired')
      case 'expiring': return t('expires_soon')
      case 'good': return 'Good'
      default: return 'No expiry set'
    }
  }

  const handleEditExpiry = (product: Product) => {
    setEditingProduct(product.id)
    setExpiryDate(product.expiry_date || '')
  }

  const handleSaveExpiry = (product: Product) => {
    const updatedProduct = {
      ...product,
      expiry_date: expiryDate || undefined
    }
    
    onUpdateProduct(updatedProduct)
    setEditingProduct(null)
    setExpiryDate('')
    
    toast({
      title: 'Product Updated',
      description: `Expiry date updated for ${product.name}`,
    })
  }

  const handleCancelEdit = () => {
    setEditingProduct(null)
    setExpiryDate('')
  }

  const expiredProducts = products.filter(p => getExpiryStatus(p) === 'expired')
  const expiringProducts = products.filter(p => getExpiryStatus(p) === 'expiring')

  return (
    <div className="space-y-4">
      {/* Alerts */}
      {expiredProducts.length > 0 && (
        <Card className="border-red-200 dark:border-red-800">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center space-x-2 text-red-600 dark:text-red-400">
              <AlertTriangle className="w-5 h-5" />
              <span>Expired Products ({expiredProducts.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {expiredProducts.slice(0, 3).map(product => (
                <div key={product.id} className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 rounded">
                  <span className="text-sm font-medium">{product.name}</span>
                  <span className="text-xs text-red-600 dark:text-red-400">
                    Expired: {product.expiry_date && new Date(product.expiry_date).toLocaleDateString()}
                  </span>
                </div>
              ))}
              {expiredProducts.length > 3 && (
                <p className="text-xs text-red-600 dark:text-red-400">
                  +{expiredProducts.length - 3} more expired products
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {expiringProducts.length > 0 && (
        <Card className="border-yellow-200 dark:border-yellow-800">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center space-x-2 text-yellow-600 dark:text-yellow-400">
              <Clock className="w-5 h-5" />
              <span>Expiring Soon ({expiringProducts.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {expiringProducts.slice(0, 3).map(product => (
                <div key={product.id} className="flex items-center justify-between p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
                  <span className="text-sm font-medium">{product.name}</span>
                  <span className="text-xs text-yellow-600 dark:text-yellow-400">
                    Expires: {product.expiry_date && new Date(product.expiry_date).toLocaleDateString()}
                  </span>
                </div>
              ))}
              {expiringProducts.length > 3 && (
                <p className="text-xs text-yellow-600 dark:text-yellow-400">
                  +{expiringProducts.length - 3} more expiring products
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Product List */}
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="w-5 h-5" />
            <span>Product Expiry Management</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-96 overflow-auto">
            {products.map(product => {
              const status = getExpiryStatus(product)
              const isEditing = editingProduct === product.id
              
              return (
                <div key={product.id} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {product.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {product.category} • {formatCurrency(product.price_dzd)} • Stock: {product.stock}
                      </p>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs ${getStatusColor(status)}`}>
                      {getStatusText(status)}
                    </div>
                  </div>
                  
                  {isEditing ? (
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <input
                        type="date"
                        value={expiryDate}
                        onChange={(e) => setExpiryDate(e.target.value)}
                        className="flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-800"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSaveExpiry(product)}
                      >
                        <Save className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancelEdit}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                        <Calendar className="w-4 h-4" />
                        <span>
                          {product.expiry_date 
                            ? new Date(product.expiry_date).toLocaleDateString()
                            : 'No expiry date set'
                          }
                        </span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditExpiry(product)}
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
