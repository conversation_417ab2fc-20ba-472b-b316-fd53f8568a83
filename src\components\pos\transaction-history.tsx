'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency } from '@/lib/utils'
import {
  Receipt,
  Edit,
  Trash2,
  Printer,
  Search,
  Calendar,
  User,
  CreditCard,
  Banknote,
} from 'lucide-react'

interface Transaction {
  id: string
  date: string
  customer_name: string
  customer_type: 'member' | 'guest'
  payment_type: 'cash' | 'credit'
  items: Array<{
    name: string
    quantity: number
    price: number
  }>
  total_amount: number
  created_at: string
}

interface TransactionHistoryProps {
  onEditTransaction?: (transaction: Transaction) => void
}

export function TransactionHistory({ onEditTransaction }: TransactionHistoryProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    loadTransactions()
  }, [])

  useEffect(() => {
    filterTransactions()
  }, [searchQuery, transactions])

  const loadTransactions = () => {
    try {
      // Load transactions from localStorage
      const storedTransactions = localStorage.getItem('gym_transactions')
      if (storedTransactions) {
        const transactionData = JSON.parse(storedTransactions)
        setTransactions(transactionData.sort((a: Transaction, b: Transaction) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        ))
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load transactions',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const filterTransactions = () => {
    if (!searchQuery.trim()) {
      setFilteredTransactions(transactions)
      return
    }

    const query = searchQuery.toLowerCase()
    const filtered = transactions.filter(transaction =>
      transaction.customer_name.toLowerCase().includes(query) ||
      transaction.id.toLowerCase().includes(query) ||
      transaction.payment_type.toLowerCase().includes(query)
    )
    setFilteredTransactions(filtered)
  }

  const handleDeleteTransaction = (transactionId: string) => {
    if (confirm('Are you sure you want to delete this transaction?')) {
      try {
        const updatedTransactions = transactions.filter(t => t.id !== transactionId)
        setTransactions(updatedTransactions)
        localStorage.setItem('gym_transactions', JSON.stringify(updatedTransactions))
        
        toast({
          title: 'Success',
          description: 'Transaction deleted successfully',
        })
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to delete transaction',
          variant: 'destructive',
        })
      }
    }
  }

  const handlePrintTransaction = (transaction: Transaction) => {
    // Create a simple receipt format
    const receiptContent = `
      ÉLITE CLUB - RH Champion Club
      ================================
      Transaction ID: ${transaction.id}
      Date: ${new Date(transaction.created_at).toLocaleString()}
      Customer: ${transaction.customer_name}
      Payment: ${transaction.payment_type.toUpperCase()}
      
      Items:
      ${transaction.items.map(item => 
        `${item.name} x${item.quantity} - ${formatCurrency(item.price * item.quantity)}`
      ).join('\n')}
      
      ================================
      Total: ${formatCurrency(transaction.total_amount)}
      ================================
      
      Thank you for your business!
      Powered by iCode DZ
      Tel: +213 551 93 05 89
    `
    
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Receipt - ${transaction.id}</title>
            <style>
              body { font-family: monospace; white-space: pre-line; }
            </style>
          </head>
          <body>${receiptContent}</body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString()
  }

  if (loading) {
    return (
      <Card className="glass border-white/20">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass border-white/20">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center space-x-2">
            <Receipt className="w-5 h-5" />
            <span>{t('transaction_history')}</span>
          </span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search transactions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
          />
        </div>

        {/* Transactions List */}
        <div className="space-y-3 max-h-96 overflow-auto">
          {filteredTransactions.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Receipt className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>{t('no_transactions')}</p>
            </div>
          ) : (
            filteredTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      #{transaction.id.slice(-8)}
                    </span>
                    <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">
                      {transaction.customer_type === 'member' ? 'Member' : 'Guest'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {transaction.payment_type === 'cash' ? (
                      <Banknote className="w-4 h-4 text-green-500" />
                    ) : (
                      <CreditCard className="w-4 h-4 text-blue-500" />
                    )}
                    <span className="text-sm font-bold text-red-600 dark:text-red-400">
                      {formatCurrency(transaction.total_amount)}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <User className="w-3 h-3 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {transaction.customer_name}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(transaction.created_at)} {formatTime(transaction.created_at)}
                    </span>
                  </div>
                </div>

                <div className="mb-3">
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                    {t('items')} ({transaction.items.length}):
                  </p>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {transaction.items.map((item, index) => (
                      <span key={index}>
                        {item.name} x{item.quantity}
                        {index < transaction.items.length - 1 && ', '}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePrintTransaction(transaction)}
                  >
                    <Printer className="w-3 h-3 mr-1" />
                    {t('print')}
                  </Button>
                  {onEditTransaction && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEditTransaction(transaction)}
                    >
                      <Edit className="w-3 h-3 mr-1" />
                      {t('edit')}
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteTransaction(transaction.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-3 h-3 mr-1" />
                    {t('delete')}
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
