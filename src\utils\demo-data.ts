// Demo data for testing the POS system

export const demoProducts = [
  {
    id: '1',
    name: 'Whey Protein Powder',
    category: 'Supplements',
    price_dzd: 5000,
    stock: 25,
    barcode: '1234567890123',
    qr_code: 'QR1234567890123',
    expiry_date: '2024-12-31'
  },
  {
    id: '2',
    name: 'Energy Drink',
    category: 'Beverages',
    price_dzd: 200,
    stock: 50,
    barcode: '2345678901234',
    qr_code: 'QR2345678901234',
    expiry_date: '2024-06-30'
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    category: 'Accessories',
    price_dzd: 800,
    stock: 15,
    barcode: '3456789012345',
    qr_code: 'QR3456789012345'
  },
  {
    id: '4',
    name: 'Pre-Workout',
    category: 'Supplements',
    price_dzd: 3500,
    stock: 12,
    barcode: '4567890123456',
    qr_code: 'QR4567890123456',
    expiry_date: '2024-07-15'
  },
  {
    id: '5',
    name: '<PERSON>tein Bar',
    category: 'Snacks',
    price_dzd: 300,
    stock: 30,
    barcode: '5678901234567',
    qr_code: 'QR5678901234567',
    expiry_date: '2024-05-20'
  },
  {
    id: '6',
    name: 'Water Bottle',
    category: 'Accessories',
    price_dzd: 1200,
    stock: 20,
    barcode: '6789012345678',
    qr_code: 'QR6789012345678'
  },
  {
    id: '7',
    name: 'BCAA Powder',
    category: 'Supplements',
    price_dzd: 4000,
    stock: 8,
    barcode: '7890123456789',
    qr_code: 'QR7890123456789',
    expiry_date: '2024-11-30'
  },
  {
    id: '8',
    name: 'Sports Drink',
    category: 'Beverages',
    price_dzd: 250,
    stock: 40,
    barcode: '8901234567890',
    qr_code: 'QR8901234567890',
    expiry_date: '2024-08-15'
  }
]

export const demoMembers = [
  {
    id: 'member1',
    full_name: 'Ahmed Benali',
    phone: '0555123456',
    email: '<EMAIL>',
    gender: 'male' as const,
    age: 28,
    situation: 'active'
  },
  {
    id: 'member2',
    full_name: 'Fatima Khelifi',
    phone: '0666789012',
    email: '<EMAIL>',
    gender: 'female' as const,
    age: 25,
    situation: 'active'
  },
  {
    id: 'member3',
    full_name: 'Mohamed Saidi',
    phone: '0777345678',
    email: '<EMAIL>',
    gender: 'male' as const,
    age: 32,
    situation: 'active'
  },
  {
    id: 'member4',
    full_name: 'Amina Boudjema',
    phone: '0888901234',
    email: '<EMAIL>',
    gender: 'female' as const,
    age: 29,
    situation: 'active'
  }
]

export const demoTransactions = [
  {
    id: 'TXN-1703123456789',
    date: '2024-01-15',
    customer_name: 'Ahmed Benali',
    customer_type: 'member' as const,
    payment_type: 'credit' as const,
    items: [
      { name: 'Whey Protein Powder', quantity: 1, price: 5000 },
      { name: 'Energy Drink', quantity: 2, price: 200 }
    ],
    total_amount: 5400,
    created_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 'TXN-1703123456790',
    date: '2024-01-15',
    customer_name: 'Client passager',
    customer_type: 'guest' as const,
    payment_type: 'cash' as const,
    items: [
      { name: 'Protein Bar', quantity: 3, price: 300 },
      { name: 'Water Bottle', quantity: 1, price: 1200 }
    ],
    total_amount: 2100,
    created_at: '2024-01-15T14:45:00Z'
  }
]

export function initializeDemoData() {
  // Initialize products
  if (!localStorage.getItem('gym_products')) {
    localStorage.setItem('gym_products', JSON.stringify(demoProducts))
  }

  // Initialize members
  if (!localStorage.getItem('gym_members')) {
    localStorage.setItem('gym_members', JSON.stringify(demoMembers))
  }

  // Initialize transactions
  if (!localStorage.getItem('gym_transactions')) {
    localStorage.setItem('gym_transactions', JSON.stringify(demoTransactions))
  }

  console.log('Demo data initialized successfully!')
}

export function clearDemoData() {
  localStorage.removeItem('gym_products')
  localStorage.removeItem('gym_members')
  localStorage.removeItem('gym_transactions')
  console.log('Demo data cleared!')
}

export function resetDemoData() {
  clearDemoData()
  initializeDemoData()
  console.log('Demo data reset successfully!')
}

// Test barcodes for scanning
export const testBarcodes = {
  'Whey Protein Powder': '1234567890123',
  'Energy Drink': '2345678901234',
  'Gym Towel': '3456789012345',
  'Pre-Workout': '4567890123456',
  'Protein Bar': '5678901234567',
  'Water Bottle': '6789012345678',
  'BCAA Powder': '7890123456789',
  'Sports Drink': '8901234567890'
}

// Test QR codes for scanning
export const testQRCodes = {
  'Whey Protein Powder': 'QR1234567890123',
  'Energy Drink': 'QR2345678901234',
  'Gym Towel': 'QR3456789012345',
  'Pre-Workout': 'QR4567890123456',
  'Protein Bar': 'QR5678901234567',
  'Water Bottle': 'QR6789012345678',
  'BCAA Powder': 'QR7890123456789',
  'Sports Drink': 'QR8901234567890'
}
