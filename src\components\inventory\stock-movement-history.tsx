'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/components/providers'
import { StockMovement, ExtendedProduct } from '@/lib/inventory-storage'
import { formatCurrency } from '@/lib/utils'
import {
  X,
  TrendingUp,
  TrendingDown,
  Package,
  ShoppingCart,
  Edit,
  RotateCcw,
  AlertTriangle,
  Search,
  Filter,
  Download,
  Calendar,
} from 'lucide-react'

interface StockMovementHistoryProps {
  onClose: () => void
  movements: StockMovement[]
  products: ExtendedProduct[]
}

export function StockMovementHistory({ onClose, movements, products }: StockMovementHistoryProps) {
  const { t } = useLanguage()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProduct, setSelectedProduct] = useState('all')
  const [selectedMovementType, setSelectedMovementType] = useState('all')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateeTo] = useState('')

  // Filter movements
  const filteredMovements = movements.filter(movement => {
    const matchesSearch = movement.product_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         movement.notes?.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesProduct = selectedProduct === 'all' || movement.product_id === selectedProduct
    
    const matchesType = selectedMovementType === 'all' || movement.movement_type === selectedMovementType
    
    const movementDate = new Date(movement.created_at).toISOString().split('T')[0]
    const matchesDateFrom = !dateFrom || movementDate >= dateFrom
    const matchesDateTo = !dateTo || movementDate <= dateTo

    return matchesSearch && matchesProduct && matchesType && matchesDateFrom && matchesDateTo
  }).sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

  const getMovementIcon = (type: StockMovement['movement_type']) => {
    switch (type) {
      case 'purchase':
        return <ShoppingCart className="w-4 h-4 text-green-500" />
      case 'sale':
        return <Package className="w-4 h-4 text-blue-500" />
      case 'adjustment':
        return <Edit className="w-4 h-4 text-orange-500" />
      case 'return':
        return <RotateCcw className="w-4 h-4 text-purple-500" />
      case 'expired':
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      default:
        return <Package className="w-4 h-4 text-gray-500" />
    }
  }

  const getMovementColor = (type: StockMovement['movement_type']) => {
    switch (type) {
      case 'purchase':
        return 'bg-green-100 text-green-800'
      case 'sale':
        return 'bg-blue-100 text-blue-800'
      case 'adjustment':
        return 'bg-orange-100 text-orange-800'
      case 'return':
        return 'bg-purple-100 text-purple-800'
      case 'expired':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const exportToCSV = () => {
    const headers = ['Date', 'Product', 'Type', 'Quantity Change', 'Previous Stock', 'New Stock', 'Notes']
    const csvData = [
      headers.join(','),
      ...filteredMovements.map(movement => [
        new Date(movement.created_at).toLocaleDateString(),
        `"${movement.product_name}"`,
        movement.movement_type,
        movement.quantity_change,
        movement.previous_stock,
        movement.new_stock,
        `"${movement.notes || ''}"`
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvData], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `stock-movements-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  // Calculate statistics
  const stats = {
    totalMovements: filteredMovements.length,
    totalPurchases: filteredMovements.filter(m => m.movement_type === 'purchase').length,
    totalSales: filteredMovements.filter(m => m.movement_type === 'sale').length,
    totalAdjustments: filteredMovements.filter(m => m.movement_type === 'adjustment').length,
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-7xl max-h-[90vh] overflow-y-auto glass border-white/20">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-6 h-6" />
            <span>Stock Movement History</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Package className="w-8 h-8 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Total Movements</p>
                    <p className="text-2xl font-bold">{stats.totalMovements}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-8 h-8 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">Purchases</p>
                    <p className="text-2xl font-bold">{stats.totalPurchases}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingDown className="w-8 h-8 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Sales</p>
                    <p className="text-2xl font-bold">{stats.totalSales}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Edit className="w-8 h-8 text-orange-500" />
                  <div>
                    <p className="text-sm text-gray-600">Adjustments</p>
                    <p className="text-2xl font-bold">{stats.totalAdjustments}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 items-end">
                <div>
                  <label className="block text-sm font-medium mb-2">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search movements..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Product</label>
                  <select
                    value={selectedProduct}
                    onChange={(e) => setSelectedProduct(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Products</option>
                    {products.map(product => (
                      <option key={product.id} value={product.id}>
                        {product.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Movement Type</label>
                  <select
                    value={selectedMovementType}
                    onChange={(e) => setSelectedMovementType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="purchase">Purchase</option>
                    <option value="sale">Sale</option>
                    <option value="adjustment">Adjustment</option>
                    <option value="return">Return</option>
                    <option value="expired">Expired</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">From Date</label>
                  <input
                    type="date"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">To Date</label>
                  <input
                    type="date"
                    value={dateTo}
                    onChange={(e) => setDateeTo(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <Button
                    onClick={exportToCSV}
                    variant="outline"
                    className="w-full"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Movements Table */}
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>
                Stock Movements ({filteredMovements.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4">Date & Time</th>
                      <th className="text-left py-3 px-4">Product</th>
                      <th className="text-left py-3 px-4">Type</th>
                      <th className="text-left py-3 px-4">Change</th>
                      <th className="text-left py-3 px-4">Previous Stock</th>
                      <th className="text-left py-3 px-4">New Stock</th>
                      <th className="text-left py-3 px-4">Notes</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredMovements.map((movement) => (
                      <tr key={movement.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium">
                              {new Date(movement.created_at).toLocaleDateString()}
                            </p>
                            <p className="text-sm text-gray-500">
                              {new Date(movement.created_at).toLocaleTimeString()}
                            </p>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <p className="font-medium">{movement.product_name}</p>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            {getMovementIcon(movement.movement_type)}
                            <span className={`px-2 py-1 rounded-full text-sm ${getMovementColor(movement.movement_type)}`}>
                              {movement.movement_type.charAt(0).toUpperCase() + movement.movement_type.slice(1)}
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`font-medium ${
                            movement.quantity_change > 0 ? 'text-green-500' : 'text-red-500'
                          }`}>
                            {movement.quantity_change > 0 ? '+' : ''}{movement.quantity_change}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-gray-600">{movement.previous_stock}</span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="font-medium">{movement.new_stock}</span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-sm text-gray-600">
                            {movement.notes || '-'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {filteredMovements.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No stock movements found matching your criteria
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}
