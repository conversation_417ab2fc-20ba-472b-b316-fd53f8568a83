"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/inventory/suppliers-modal.tsx":
/*!******************************************************!*\
  !*** ./src/components/inventory/suppliers-modal.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SuppliersModal: function() { return /* binding */ SuppliersModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,DollarSign,Edit,Eye,FileText,Hash,Mail,MapPin,Phone,Plus,Save,Search,Trash2,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ SuppliersModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SuppliersModal(param) {\n    let { onClose, onUpdate } = param;\n    _s();\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingSupplier, setEditingSupplier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedSupplier, setSelectedSupplier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTransactions, setShowTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPaymentForm, setShowPaymentForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        contact_person: \"\",\n        phone: \"\",\n        email: \"\",\n        address: \"\",\n        tax_number: \"\",\n        payment_terms: \"cash\",\n        credit_limit: \"0\",\n        notes: \"\"\n    });\n    // Payment form state\n    const [paymentData, setPaymentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: \"\",\n        payment_method: \"cash\",\n        reference_number: \"\",\n        notes: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSuppliers();\n    }, []);\n    const loadSuppliers = ()=>{\n        const suppliersData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getSuppliers();\n        setSuppliers(suppliersData.filter((s)=>s.active));\n    };\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            contact_person: \"\",\n            phone: \"\",\n            email: \"\",\n            address: \"\",\n            tax_number: \"\",\n            payment_terms: \"cash\",\n            credit_limit: \"0\",\n            notes: \"\"\n        });\n        setEditingSupplier(null);\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleEdit = (supplier)=>{\n        setFormData({\n            name: supplier.name,\n            contact_person: supplier.contact_person || \"\",\n            phone: supplier.phone || \"\",\n            email: supplier.email || \"\",\n            address: supplier.address || \"\",\n            tax_number: supplier.tax_number || \"\",\n            payment_terms: supplier.payment_terms,\n            credit_limit: supplier.credit_limit.toString(),\n            notes: supplier.notes || \"\"\n        });\n        setEditingSupplier(supplier);\n        setShowAddForm(true);\n    };\n    const handleSave = ()=>{\n        if (!formData.name.trim()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Supplier name is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const supplierData = {\n                name: formData.name.trim(),\n                contact_person: formData.contact_person || undefined,\n                phone: formData.phone || undefined,\n                email: formData.email || undefined,\n                address: formData.address || undefined,\n                tax_number: formData.tax_number || undefined,\n                payment_terms: formData.payment_terms,\n                credit_limit: parseFloat(formData.credit_limit) || 0,\n                current_balance: (editingSupplier === null || editingSupplier === void 0 ? void 0 : editingSupplier.current_balance) || 0,\n                notes: formData.notes || undefined,\n                active: true\n            };\n            if (editingSupplier) {\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.updateSupplier(editingSupplier.id, supplierData);\n                toast({\n                    title: \"Success\",\n                    description: \"Supplier updated successfully\"\n                });\n            } else {\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.addSupplier(supplierData);\n                toast({\n                    title: \"Success\",\n                    description: \"Supplier added successfully\"\n                });\n            }\n            loadSuppliers();\n            setShowAddForm(false);\n            resetForm();\n            onUpdate();\n        } catch (error) {\n            console.error(\"Error saving supplier:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save supplier\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDelete = (supplier)=>{\n        if (confirm('Are you sure you want to delete supplier \"'.concat(supplier.name, '\"?'))) {\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.deleteSupplier(supplier.id);\n            loadSuppliers();\n            toast({\n                title: \"Success\",\n                description: \"Supplier deleted successfully\"\n            });\n            onUpdate();\n        }\n    };\n    const handlePayment = ()=>{\n        if (!selectedSupplier || !paymentData.amount) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Amount is required\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.addSupplierPayment({\n                supplier_id: selectedSupplier.id,\n                payment_date: new Date().toISOString().split(\"T\")[0],\n                amount: parseFloat(paymentData.amount),\n                payment_method: paymentData.payment_method,\n                reference_number: paymentData.reference_number || undefined,\n                notes: paymentData.notes || undefined\n            });\n            toast({\n                title: \"Success\",\n                description: \"Payment recorded successfully\"\n            });\n            loadSuppliers();\n            setShowPaymentForm(false);\n            setPaymentData({\n                amount: \"\",\n                payment_method: \"cash\",\n                reference_number: \"\",\n                notes: \"\"\n            });\n            onUpdate();\n        } catch (error) {\n            console.error(\"Error recording payment:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to record payment\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredSuppliers = suppliers.filter((supplier)=>{\n        var _supplier_contact_person, _supplier_phone;\n        return supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) || ((_supplier_contact_person = supplier.contact_person) === null || _supplier_contact_person === void 0 ? void 0 : _supplier_contact_person.toLowerCase().includes(searchQuery.toLowerCase())) || ((_supplier_phone = supplier.phone) === null || _supplier_phone === void 0 ? void 0 : _supplier_phone.includes(searchQuery));\n    });\n    const getSupplierTransactions = (supplierId)=>{\n        const purchases = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getPurchases().filter((p)=>p.supplier_id === supplierId);\n        const payments = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getSupplierPaymentsBySupplier(supplierId);\n        return {\n            purchases,\n            payments\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n        onClick: (e)=>{\n            if (e.target === e.currentTarget) {\n            // Don't close modal when clicking outside\n            }\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-6xl max-h-[90vh] overflow-y-auto glass border-white/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Suppliers Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: onClose,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        !showAddForm && !showTransactions && !showPaymentForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex-1 max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search suppliers...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>setShowAddForm(true),\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Supplier\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Supplier\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Contact\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Payment Terms\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Balance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: filteredSuppliers.map((supplier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-100 hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: supplier.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 297,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            supplier.contact_person && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: supplier.contact_person\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            supplier.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center text-sm\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-3 h-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                        lineNumber: 307,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    supplier.phone\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            supplier.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center text-sm\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"w-3 h-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                        lineNumber: 313,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    supplier.email\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-sm \".concat(supplier.payment_terms === \"cash\" ? \"bg-green-100 text-green-800\" : \"bg-blue-100 text-blue-800\"),\n                                                                        children: supplier.payment_terms.replace(\"_\", \" \").toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium \".concat(supplier.current_balance > 0 ? \"text-red-500\" : \"text-green-500\"),\n                                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(supplier.current_balance)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            supplier.payment_terms !== \"cash\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Limit: \",\n                                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(supplier.credit_limit)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedSupplier(supplier);\n                                                                                    setShowTransactions(true);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleEdit(supplier),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                    lineNumber: 359,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            supplier.current_balance > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>{\n                                                                                    setSelectedSupplier(supplier);\n                                                                                    setShowPaymentForm(true);\n                                                                                },\n                                                                                className: \"text-green-600 hover:text-green-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                    lineNumber: 371,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>handleDelete(supplier),\n                                                                                className: \"text-red-500 hover:text-red-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                    lineNumber: 380,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, supplier.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this),\n                                        filteredSuppliers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-gray-500\",\n                                            children: \"No suppliers found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: editingSupplier ? \"Edit Supplier\" : \"Add New Supplier\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>{\n                                                setShowAddForm(false);\n                                                resetForm();\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Supplier Name *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.name,\n                                                            onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"Enter supplier name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"Contact Person\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.contact_person,\n                                                            onChange: (e)=>handleInputChange(\"contact_person\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"Enter contact person name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Phone\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: formData.phone,\n                                                            onChange: (e)=>handleInputChange(\"phone\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"Enter phone number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Email\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: formData.email,\n                                                            onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"Enter email address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Address\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: formData.address,\n                                                            onChange: (e)=>handleInputChange(\"address\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            rows: 3,\n                                                            placeholder: \"Enter address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Tax Number\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.tax_number,\n                                                            onChange: (e)=>handleInputChange(\"tax_number\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"Enter tax number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Payment Terms\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: formData.payment_terms,\n                                                            onChange: (e)=>handleInputChange(\"payment_terms\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"cash\",\n                                                                    children: \"Cash\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"credit_30\",\n                                                                    children: \"Credit 30 days\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"credit_60\",\n                                                                    children: \"Credit 60 days\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"credit_90\",\n                                                                    children: \"Credit 90 days\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 19\n                                                }, this),\n                                                formData.payment_terms !== \"cash\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"Credit Limit (DZD)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: formData.credit_limit,\n                                                            onChange: (e)=>handleInputChange(\"credit_limit\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"0\",\n                                                            min: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"w-4 h-4 inline mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Notes\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: formData.notes,\n                                                            onChange: (e)=>handleInputChange(\"notes\", e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                            rows: 3,\n                                                            placeholder: \"Enter notes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-4 pt-6 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>{\n                                                setShowAddForm(false);\n                                                resetForm();\n                                            },\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleSave,\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this),\n                                                editingSupplier ? \"Update Supplier\" : \"Add Supplier\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this),\n                        showPaymentForm && selectedSupplier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: [\n                                                \"Record Payment - \",\n                                                selectedSupplier.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>setShowPaymentForm(false),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Current Balance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-red-500\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(selectedSupplier.current_balance)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"Payment Amount (DZD) *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: paymentData.amount,\n                                                    onChange: (e)=>setPaymentData((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"0.00\",\n                                                    min: \"0\",\n                                                    max: selectedSupplier.current_balance,\n                                                    step: \"0.01\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"Payment Method\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: paymentData.payment_method,\n                                                    onChange: (e)=>setPaymentData((prev)=>({\n                                                                ...prev,\n                                                                payment_method: e.target.value\n                                                            })),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"cash\",\n                                                            children: \"Cash\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"bank_transfer\",\n                                                            children: \"Bank Transfer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"check\",\n                                                            children: \"Check\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"card\",\n                                                            children: \"Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"Reference Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: paymentData.reference_number,\n                                                    onChange: (e)=>setPaymentData((prev)=>({\n                                                                ...prev,\n                                                                reference_number: e.target.value\n                                                            })),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"Enter reference number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"Notes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: paymentData.notes,\n                                                    onChange: (e)=>setPaymentData((prev)=>({\n                                                                ...prev,\n                                                                notes: e.target.value\n                                                            })),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"Enter notes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-4 pt-6 border-t\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowPaymentForm(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handlePayment,\n                                            className: \"bg-green-500 hover:bg-green-600 text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Record Payment\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this),\n                        showTransactions && selectedSupplier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: [\n                                                \"Transactions - \",\n                                                selectedSupplier.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>setShowTransactions(false),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_DollarSign_Edit_Eye_FileText_Hash_Mail_MapPin_Phone_Plus_Save_Search_Trash2_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"glass border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Current Balance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-red-500\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(selectedSupplier.current_balance)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"glass border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Credit Limit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(selectedSupplier.credit_limit)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"glass border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Available Credit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-500\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(selectedSupplier.credit_limit - selectedSupplier.current_balance)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium\",\n                                            children: \"Recent Transactions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-2 px-4\",\n                                                                    children: \"Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-2 px-4\",\n                                                                    children: \"Type\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-2 px-4\",\n                                                                    children: \"Amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-2 px-4\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: (()=>{\n                                                            const { purchases, payments } = getSupplierTransactions(selectedSupplier.id);\n                                                            const allTransactions = [\n                                                                ...purchases.map((p)=>({\n                                                                        ...p,\n                                                                        type: \"purchase\"\n                                                                    })),\n                                                                ...payments.map((p)=>({\n                                                                        ...p,\n                                                                        type: \"payment\"\n                                                                    }))\n                                                            ].sort((a, b)=>new Date(b.created_at || b.payment_date).getTime() - new Date(a.created_at || a.payment_date).getTime());\n                                                            return allTransactions.slice(0, 10).map((transaction, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"border-b border-gray-100\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 px-4\",\n                                                                            children: new Date(transaction.type === \"purchase\" ? transaction.created_at : transaction.payment_date).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 px-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded-full text-sm \".concat(transaction.type === \"purchase\" ? \"bg-blue-100 text-blue-800\" : \"bg-green-100 text-green-800\"),\n                                                                                children: transaction.type === \"purchase\" ? \"Purchase\" : \"Payment\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 px-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: transaction.type === \"purchase\" ? \"text-red-500\" : \"text-green-500\",\n                                                                                children: [\n                                                                                    transaction.type === \"purchase\" ? \"+\" : \"-\",\n                                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(transaction.type === \"purchase\" ? transaction.total_amount : transaction.amount)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 759,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                            lineNumber: 758,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 px-4\",\n                                                                            children: transaction.type === \"purchase\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded-full text-sm \".concat(transaction.payment_status === \"paid\" ? \"bg-green-100 text-green-800\" : transaction.payment_status === \"partial\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                                children: transaction.payment_status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 33\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm\",\n                                                                                children: \"Completed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                                lineNumber: 776,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 27\n                                                                }, this));\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\suppliers-modal.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(SuppliersModal, \"Yx8gDxBkTRuVDk5R2M5vq7Jm39E=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = SuppliersModal;\nvar _c;\n$RefreshReg$(_c, \"SuppliersModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/inventory/suppliers-modal.tsx\n"));

/***/ })

});