'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import { InventoryStorage, ExtendedProduct, Category } from '@/lib/inventory-storage'
import {
  X,
  Save,
  Scan,
  Upload,
  Package,
  DollarSign,
  Calendar,
  Hash,
  Tag,
  FileText,
  Building,
  Ruler,
} from 'lucide-react'
import { ScannerModal } from '@/components/pos/scanner-modal'

interface AddProductModalProps {
  product?: ExtendedProduct | null
  onClose: () => void
  onSave: () => void
}

export function AddProductModal({ product, onClose, onSave }: AddProductModalProps) {
  const { t } = useLanguage()
  const { toast } = useToast()

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    price_dzd: '',
    stock: '',
    min_stock: '5',
    expiry_date: '',
    image_url: '',
    barcode: '',
    qr_code: '',
    description: '',
    brand: '',
    unit: 'piece',
  })

  const [categories, setCategories] = useState<Category[]>([])
  const [showScanner, setShowScanner] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Load categories and populate form if editing
  useEffect(() => {
    const categoriesData = InventoryStorage.getCategories()
    setCategories(categoriesData.filter(c => c.active))

    if (product) {
      setFormData({
        name: product.name,
        category: product.category,
        price_dzd: product.price_dzd.toString(),
        stock: product.stock.toString(),
        min_stock: product.min_stock.toString(),
        expiry_date: product.expiry_date || '',
        image_url: product.image_url || '',
        barcode: product.barcode || '',
        qr_code: product.qr_code || '',
        description: product.description || '',
        brand: product.brand || '',
        unit: product.unit,
      })
    }
  }, [product])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleScanBarcode = () => {
    setShowScanner(true)
  }

  const handleScanSuccess = (scannedCode: string) => {
    setFormData(prev => ({
      ...prev,
      barcode: scannedCode
    }))

    toast({
      title: 'Barcode Scanned',
      description: `Barcode: ${scannedCode}`,
    })
    setShowScanner(false)
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // For now, we'll create a local URL
      // In a real implementation, you would upload to a server or cloud storage
      const imageUrl = URL.createObjectURL(file)
      setFormData(prev => ({
        ...prev,
        image_url: imageUrl
      }))
      
      toast({
        title: 'Image Uploaded',
        description: 'Product image has been uploaded successfully',
      })
    }
  }

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Product name is required',
        variant: 'destructive',
      })
      return false
    }

    if (!formData.category) {
      toast({
        title: 'Validation Error',
        description: 'Category is required',
        variant: 'destructive',
      })
      return false
    }

    if (!formData.price_dzd || parseFloat(formData.price_dzd) <= 0) {
      toast({
        title: 'Validation Error',
        description: 'Valid price is required',
        variant: 'destructive',
      })
      return false
    }

    if (!formData.stock || parseInt(formData.stock) < 0) {
      toast({
        title: 'Validation Error',
        description: 'Valid stock quantity is required',
        variant: 'destructive',
      })
      return false
    }

    return true
  }

  const handleSave = async () => {
    if (!validateForm()) return

    setIsLoading(true)
    try {
      const productData: Omit<ExtendedProduct, 'id' | 'created_at' | 'updated_at'> = {
        name: formData.name.trim(),
        category: formData.category,
        price_dzd: parseFloat(formData.price_dzd),
        stock: parseInt(formData.stock),
        min_stock: parseInt(formData.min_stock),
        expiry_date: formData.expiry_date || undefined,
        image_url: formData.image_url || undefined,
        barcode: formData.barcode || undefined,
        qr_code: formData.qr_code || undefined,
        description: formData.description || undefined,
        brand: formData.brand || undefined,
        unit: formData.unit,
      }

      if (product) {
        // Update existing product
        const products = InventoryStorage.getFromStorage<ExtendedProduct>('gym_products')
        const index = products.findIndex(p => p.id === product.id)
        if (index !== -1) {
          products[index] = {
            ...products[index],
            ...productData,
            updated_at: new Date().toISOString(),
          }
          InventoryStorage.saveToStorage('gym_products', products)
        }
      } else {
        // Add new product
        const products = InventoryStorage.getFromStorage<ExtendedProduct>('gym_products')
        const newProduct: ExtendedProduct = {
          ...productData,
          id: Date.now().toString(36) + Math.random().toString(36).substr(2),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
        products.push(newProduct)
        InventoryStorage.saveToStorage('gym_products', products)
      }

      toast({
        title: 'Success',
        description: `Product ${product ? 'updated' : 'added'} successfully`,
      })

      onSave()
      onClose()
    } catch (error) {
      console.error('Error saving product:', error)
      toast({
        title: 'Error',
        description: 'Failed to save product',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto glass border-white/20">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Package className="w-6 h-6" />
            <span>{product ? 'Edit Product' : 'Add New Product'}</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    <Package className="w-4 h-4 inline mr-2" />
                    Product Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter product name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    <Tag className="w-4 h-4 inline mr-2" />
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select category</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.name}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    <Building className="w-4 h-4 inline mr-2" />
                    Brand
                  </label>
                  <input
                    type="text"
                    value={formData.brand}
                    onChange={(e) => handleInputChange('brand', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter brand name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    <FileText className="w-4 h-4 inline mr-2" />
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="Enter product description"
                  />
                </div>
              </div>

              {/* Pricing and Stock */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Pricing & Stock</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <DollarSign className="w-4 h-4 inline mr-2" />
                      Price (DZD) *
                    </label>
                    <input
                      type="number"
                      value={formData.price_dzd}
                      onChange={(e) => handleInputChange('price_dzd', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <Ruler className="w-4 h-4 inline mr-2" />
                      Unit
                    </label>
                    <select
                      value={formData.unit}
                      onChange={(e) => handleInputChange('unit', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="piece">Piece</option>
                      <option value="kg">Kilogram</option>
                      <option value="liter">Liter</option>
                      <option value="box">Box</option>
                      <option value="bottle">Bottle</option>
                      <option value="pack">Pack</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Current Stock *
                    </label>
                    <input
                      type="number"
                      value={formData.stock}
                      onChange={(e) => handleInputChange('stock', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="0"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Minimum Stock
                    </label>
                    <input
                      type="number"
                      value={formData.min_stock}
                      onChange={(e) => handleInputChange('min_stock', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="5"
                      min="0"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    <Calendar className="w-4 h-4 inline mr-2" />
                    Expiry Date
                  </label>
                  <input
                    type="date"
                    value={formData.expiry_date}
                    onChange={(e) => handleInputChange('expiry_date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* Product Image */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Product Image</h3>
                
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {formData.image_url ? (
                    <div className="space-y-4">
                      <img
                        src={formData.image_url}
                        alt="Product preview"
                        className="w-32 h-32 object-cover rounded-lg mx-auto"
                      />
                      <Button
                        variant="outline"
                        onClick={() => handleInputChange('image_url', '')}
                      >
                        Remove Image
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                      <div>
                        <label className="cursor-pointer">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                          <Button variant="outline" asChild>
                            <span>Upload Image</span>
                          </Button>
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Barcode & QR Code */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Identification</h3>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    <Hash className="w-4 h-4 inline mr-2" />
                    Barcode
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={formData.barcode}
                      onChange={(e) => handleInputChange('barcode', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter or scan barcode"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleScanBarcode}
                    >
                      <Scan className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    QR Code
                  </label>
                  <input
                    type="text"
                    value={formData.qr_code}
                    onChange={(e) => handleInputChange('qr_code', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter QR code"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSave}
              disabled={isLoading}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? 'Saving...' : (product ? 'Update Product' : 'Add Product')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Scanner Modal */}
      <ScannerModal
        isOpen={showScanner}
        onClose={() => setShowScanner(false)}
        onScanSuccess={handleScanSuccess}
        scanType="barcode"
      />
    </div>
  )
}
