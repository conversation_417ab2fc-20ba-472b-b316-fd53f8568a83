"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/inventory/add-product-modal.tsx":
/*!********************************************************!*\
  !*** ./src/components/inventory/add-product-modal.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddProductModal: function() { return /* binding */ AddProductModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,DollarSign,FileText,Hash,Package,Ruler,Save,Scan,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ AddProductModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction AddProductModal(param) {\n    let { product, onClose, onSave } = param;\n    _s();\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Form state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        category: \"\",\n        price_dzd: \"\",\n        stock: \"\",\n        min_stock: \"5\",\n        expiry_date: \"\",\n        image_url: \"\",\n        barcode: \"\",\n        qr_code: \"\",\n        description: \"\",\n        brand: \"\",\n        unit: \"piece\"\n    });\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load categories and populate form if editing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const categoriesData = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getCategories();\n        setCategories(categoriesData.filter((c)=>c.active));\n        if (product) {\n            setFormData({\n                name: product.name || \"\",\n                category: product.category || \"\",\n                price_dzd: (product.price_dzd || 0).toString(),\n                stock: (product.stock || 0).toString(),\n                min_stock: (product.min_stock || 5).toString(),\n                expiry_date: product.expiry_date || \"\",\n                image_url: product.image_url || \"\",\n                barcode: product.barcode || \"\",\n                qr_code: product.qr_code || \"\",\n                description: product.description || \"\",\n                brand: product.brand || \"\",\n                unit: product.unit || \"piece\"\n            });\n        }\n    }, [\n        product\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleScanBarcode = ()=>{\n        setShowScanner(true);\n    };\n    const handleScanSuccess = (scannedCode)=>{\n        setFormData((prev)=>({\n                ...prev,\n                barcode: scannedCode\n            }));\n        toast({\n            title: \"Barcode Scanned\",\n            description: \"Barcode: \".concat(scannedCode)\n        });\n        setShowScanner(false);\n    };\n    const handleImageUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // For now, we'll create a local URL\n            // In a real implementation, you would upload to a server or cloud storage\n            const imageUrl = URL.createObjectURL(file);\n            setFormData((prev)=>({\n                    ...prev,\n                    image_url: imageUrl\n                }));\n            toast({\n                title: \"Image Uploaded\",\n                description: \"Product image has been uploaded successfully\"\n            });\n        }\n    };\n    const validateForm = ()=>{\n        if (!formData.name.trim()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Product name is required\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (!formData.category) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Category is required\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (!formData.price_dzd || parseFloat(formData.price_dzd) <= 0) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Valid price is required\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (!formData.stock || parseInt(formData.stock) < 0) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Valid stock quantity is required\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        if (!validateForm()) return;\n        setIsLoading(true);\n        try {\n            const productData = {\n                name: formData.name.trim(),\n                category: formData.category,\n                price_dzd: parseFloat(formData.price_dzd),\n                stock: parseInt(formData.stock),\n                min_stock: parseInt(formData.min_stock),\n                expiry_date: formData.expiry_date || undefined,\n                image_url: formData.image_url || undefined,\n                barcode: formData.barcode || undefined,\n                qr_code: formData.qr_code || undefined,\n                description: formData.description || undefined,\n                brand: formData.brand || undefined,\n                unit: formData.unit\n            };\n            if (product) {\n                // Update existing product\n                const products = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getFromStorage(\"gym_products\");\n                const index = products.findIndex((p)=>p.id === product.id);\n                if (index !== -1) {\n                    products[index] = {\n                        ...products[index],\n                        ...productData,\n                        updated_at: new Date().toISOString()\n                    };\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.saveToStorage(\"gym_products\", products);\n                }\n            } else {\n                // Add new product\n                const products = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getFromStorage(\"gym_products\");\n                const newProduct = {\n                    ...productData,\n                    id: Date.now().toString(36) + Math.random().toString(36).substr(2),\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                };\n                products.push(newProduct);\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.saveToStorage(\"gym_products\", products);\n            }\n            toast({\n                title: \"Success\",\n                description: \"Product \".concat(product ? \"updated\" : \"added\", \" successfully\")\n            });\n            onSave();\n            onClose();\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save product\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n        onClick: (e)=>{\n            if (e.target === e.currentTarget) {\n            // Don't close modal when clicking outside\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full max-w-4xl max-h-[90vh] overflow-y-auto glass border-white/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: product ? \"Edit Product\" : \"Add New Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Basic Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Product Name *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.name,\n                                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                                                placeholder: \"Enter product name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Category *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.category,\n                                                                onChange: (e)=>handleInputChange(\"category\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Select category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category.name,\n                                                                            children: category.name\n                                                                        }, category.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Brand\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.brand,\n                                                                onChange: (e)=>handleInputChange(\"brand\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                                                placeholder: \"Enter brand name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Description\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: formData.description,\n                                                                onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                                                rows: 3,\n                                                                placeholder: \"Enter product description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Pricing & Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4 inline mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 321,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \"Price (DZD) *\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: formData.price_dzd,\n                                                                        onChange: (e)=>handleInputChange(\"price_dzd\", e.target.value),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        placeholder: \"0.00\",\n                                                                        min: \"0\",\n                                                                        step: \"0.01\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"w-4 h-4 inline mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \"Unit\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: formData.unit,\n                                                                        onChange: (e)=>handleInputChange(\"unit\", e.target.value),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"piece\",\n                                                                                children: \"Piece\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"kg\",\n                                                                                children: \"Kilogram\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"liter\",\n                                                                                children: \"Liter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"box\",\n                                                                                children: \"Box\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 348,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"bottle\",\n                                                                                children: \"Bottle\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 349,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"pack\",\n                                                                                children: \"Pack\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: \"Current Stock *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: formData.stock,\n                                                                        onChange: (e)=>handleInputChange(\"stock\", e.target.value),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        placeholder: \"0\",\n                                                                        min: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium mb-2\",\n                                                                        children: \"Minimum Stock\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: formData.min_stock,\n                                                                        onChange: (e)=>handleInputChange(\"min_stock\", e.target.value),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        placeholder: \"5\",\n                                                                        min: \"0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Expiry Date\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.expiry_date,\n                                                                onChange: (e)=>handleInputChange(\"expiry_date\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Product Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                        children: formData.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: formData.image_url,\n                                                                    alt: \"Product preview\",\n                                                                    className: \"w-32 h-32 object-cover rounded-lg mx-auto\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>handleInputChange(\"image_url\", \"\"),\n                                                                    children: \"Remove Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-12 h-12 text-gray-400 mx-auto\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"cursor-pointer\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"file\",\n                                                                                accept: \"image/*\",\n                                                                                onChange: handleImageUpload,\n                                                                                className: \"hidden\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"outline\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Upload Image\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                    lineNumber: 433,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Identification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4 inline mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Barcode\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: formData.barcode,\n                                                                        onChange: (e)=>handleInputChange(\"barcode\", e.target.value),\n                                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                        placeholder: \"Enter or scan barcode\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"outline\",\n                                                                        onClick: handleScanBarcode,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"QR Code\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.qr_code,\n                                                                onChange: (e)=>handleInputChange(\"qr_code\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n                                                                placeholder: \"Enter QR code\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6 border-t\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        onClick: onClose,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSave,\n                                        disabled: isLoading,\n                                        className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_DollarSign_FileText_Hash_Package_Ruler_Save_Scan_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, this),\n                                            isLoading ? \"Saving...\" : product ? \"Update Product\" : \"Add Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_7__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: \"barcode\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\add-product-modal.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s(AddProductModal, \"PbW2vU2V8Iaib6AnJieGpPmv/+Q=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = AddProductModal;\nvar _c;\n$RefreshReg$(_c, \"AddProductModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/inventory/add-product-modal.tsx\n"));

/***/ })

});